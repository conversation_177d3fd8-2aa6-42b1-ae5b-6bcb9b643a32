# AiTenderAnalysis Frontend

An Angular 19+ portal generated with [Angular CLI](https://github.com/angular/angular-cli) using Microsoft Entra ID (MSAL), OpenAPI client generation, full i18n support, Tailwind CSS, and [Angular Material components](https://material.angular.dev/components/categories).
Deployed as an Azure Static Web App.

---

## Table of Contents

* [Getting Started](#getting-started)
* [Development](#development)
* [Environment & Secrets](#environment--secrets)
* [Authentication](#authentication)
* [Internationalization (i18n)](#internationalization-i18n)
* [API Client Generation](#api-client-generation)
* [Configuration Files](#configuration-files)
* [Code Quality & Tooling](#code-quality--tooling)
* [Deployment](#deployment)
* [CI/CD Pipeline](#cicd-pipeline)
* [Useful Commands](#useful-commands)

---

## Getting Started
### Install Node.js

You need Node.js (LTS version recommended) and npm.
Check your current versions:

```bash
node -v
npm -v
```

#### **On Windows:**

* Easiest: Download and install from [nodejs.org](https://nodejs.org/)
* Or use [Chocolatey](https://chocolatey.org/install) (requires admin rights):

  ```bash
  choco install nodejs-lts
  ```

#### **On Mac:**

* Download from [nodejs.org](https://nodejs.org/)
* Or use [Homebrew](https://brew.sh/):

  ```bash
  brew install node@lts
  ```

If you run into issues with permissions or path, restart your terminal or shell after installing Node and npm.

### Set up the project

1. **Clone the Repository:**

   ```bash
   git clone <repo-url>
   cd frontend
   ```

2. **Install Dependencies:**

   ```bash
   npm install
   ```

3. **Install Angular CLI (if needed):**

   ```bash
   npm i -g @angular/cli
   ```

4. **Run the Application:**

   ```bash
   ng serve
   ```

Open [http://localhost:4200/](http://localhost:4200/) in your browser.

---

## Development

* Main source code lives in `src/app/`.
* Use Angular CLI for generating new code:

  ```bash
  ng g component <path>/<component-name> --flat
  ```
* Style with SCSS and Tailwind CSS (see [Configuration Files](#configuration-files)).

---

## Environment & Secrets

* **Configuration** is handled via files in `src/environments/`:

  * `environment.ts` (development/local)
  * `environment.prod.ts` (production)
* Both environment files include API endpoints and all MSAL (Microsoft Entra ID) settings.
* **Important:**

  * **Never commit real secrets or production tokens**. If you ever need to use secrets or tokens, use environment variables or a `.env` pattern and make sure they are excluded via `.gitignore`.
* The config is typed using `environment.model.ts` for type safety.

---

## Authentication

* Uses [MSAL for Angular](https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/lib/msal-angular) to authenticate via Microsoft Entra ID.
* All MSAL logic and configuration is in `src/app/core/auth/msal/`.
* **Configuration** (clientId, authority, scopes, etc.) is set via `environment.ts` files.
* Tokens are attached to all backend requests by the MSAL HTTP interceptor.
* For local dev, use a Microsoft Entra ID account (from Gofore) – see `environment.ts` for the local redirect URI and scopes.

---

## Internationalization (i18n)

* Uses [ngx-translate](https://github.com/ngx-translate/core).
* All translation files live in `public/i18n/` (e.g. `en.json`, `de.json`, `fi.json`).
* To extract or update translation keys from code, use:

  ```bash
  npm run i18n:extract
  ```

  * This scans your codebase for translation keys and merges them into the JSON files.
  * Extraction scripts are in the `scripts/` directory.
* The translation loader is configured in `app.config.ts`.

---

## API Client Generation

* **Backend API contract**: `openapi/aita-api.yaml`
* **Client code**: `src/app/api/`
* To **update the client** when the backend changes:

  1. Replace the `aita-api.yaml` file with the new OpenAPI spec from the backend team.
  2. Run:

     ```bash
     npm run generate:api
     ```
  3. Check in the new or updated `src/app/api/` files.
* The API base path used here is set via `BASE_PATH`.
* At the moment, we have no automated OpenAPI generation in CI/CD (manual workflow). But we could use Azure Artifacts to generate our own project npm package. This was tried out and did work but for simplicity we for now use the manual approach.

---

## Configuration Files

* **staticwebapp.config.json**

  * Handles routing and asset serving for Azure Static Web Apps (SPA fallback, MIME types, etc).
* **tailwind.config.js**

  * Sets up custom Tailwind theme using CSS variables, used in all `src/**/*.html|ts|scss`.
* **angular.json**

  * Angular CLI build and asset config, including style, assets, and path aliases.
* **tsconfig.json / tsconfig.app.json**

  * TypeScript config, including base URL, strictness, and module resolution.
* **.editorconfig, .prettierrc, .eslintrc.js**

  * Style and formatting rules, enforced by Husky and lint-staged.

---

## Code Quality & Tooling

* **Linting**:

  ```bash
  npm run lint      # Lint code
  npm run lint:fix  # Auto-fix
  ```
* **Formatting**:

  ```bash
  npm run format    # Format code with Prettier
  ```
* **Pre-commit hooks**:
  Uses Husky and lint-staged to run lint/format before commits:

  ```bash
  npm run prepare   # Run once after clone to activate hooks
  ```
* **VSCode**:
  Recommended: install `esbenp.prettier-vscode` for formatting on save.

---

## Deployment

### Manual (Azure Static Web App)

1. **Build for production:**

   ```bash
   ng build
   ```

2. **Deploy (manual):**

   ```bash
   swa deploy ./dist/ai-tender-analysis/browser/ --deployment-token <token> --env Production
   ```

* Get the deployment token:

  ```bash
  az staticwebapp secrets list --name <app-name> --query "properties.apiKey" --output tsv
  ```

3. **Recommended:**
   Use Azure DevOps pipeline for repeatable deployments (see below).

---

## CI/CD Pipeline

* See `azure-pipelines.yaml` for full automation details.
* Pipeline stages:

  1. **ExecuteTerraform**

  * Initializes and applies Terraform for infrastructure as code.
  * Manual validation step before apply.
  2. **DeployPortal**

  * Builds and deploys the Angular frontend as an Azure Static Web App.
* **Pipeline variables** (tokens, secrets, environment-specific values) are managed via Azure DevOps variable groups and **should not be stored in code**.
* The pipeline builds to `dist/ai-tender-analysis/browser`.

---

## Useful Commands

| Task                 | Command                |
| -------------------- | ---------------------- |
| Start dev server     | `ng serve`             |
| Build (prod)         | `ng build`             |
| Lint code            | `npm run lint`         |
| Auto-fix lint        | `npm run lint:fix`     |
| Format code          | `npm run format`       |
| Extract i18n strings | `npm run i18n:extract` |
| Generate API client  | `npm run generate:api` |
| Run tests            | `ng test`              |
| Prepare Husky hooks  | `npm run prepare`      |

---

## Best Practices

* **Never commit secrets or production keys** to `environment.ts` or anywhere in the repository. Use environment variables or Azure DevOps secrets for anything sensitive.
* Keep `aita-api.yaml` in sync with the backend contract; regenerate the API client as needed.
* Keep translation files up to date by regularly running the extraction script after UI changes.
* For any changes to authentication or scopes, coordinate with backend and update `environment.ts` and MSAL config.

---
