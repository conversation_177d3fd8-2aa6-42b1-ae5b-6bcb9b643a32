variables:
  - name: workingDirectoryTerraform # This variable allows managing different configurations in the same repository
    value: $(System.DefaultWorkingDirectory)/iac/terraform
  - group: common-variables
  - group: frontend

trigger: none

pool:
  vmImage: 'ubuntu-latest'

jobs:
# ManualValidation task is used to confirm destroying Resources managed by Terraform. It must be
# placed in a separate job since it's an agent-less job.
- job: Validate
  displayName: Manual validation
  continueOnError: false
  pool: server
  steps:
  - task: ManualValidation@0
    displayName: Validate Terraform plan
    timeoutInMinutes: 60 # task times out in 1 day
    inputs:
      instructions: 'Do you really want to destroy all Resources managed by Terraform?'

- job: TerraformDestroy
  displayName: Terraform destroy
  condition: succeeded()
  dependsOn: Validate
  continueOnError: false
  steps:
  - task: TerraformInstaller@1
    displayName: Install Terraform
    inputs:
      terraformVersion: $(terraformVersion)

  - task: TerraformTaskV4@4
    displayName: Initialize Terraform
    inputs:
      provider: 'azurerm'
      command: 'init'
      workingDirectory: $(workingDirectoryTerraform)
      backendServiceArm: $(serviceConnection)
      backendAzureRmResourceGroupName: $(tfBackendStorageAccountResourceGroup)
      backendAzureRmStorageAccountName: $(tfBackendStorageAccountName)
      backendAzureRmContainerName: $(tfBackendStorageAccountContainerName)
      backendAzureRmKey: $(tfBackendStorageAccountKey)

  - task: TerraformTaskV4@4
    displayName: Destroy Resources
    inputs:
      provider: 'azurerm'
      command: 'destroy'
      commandOptions: '-auto-approve'
      workingDirectory: $(workingDirectoryTerraform)
      environmentServiceNameAzureRM: $(serviceConnection)
