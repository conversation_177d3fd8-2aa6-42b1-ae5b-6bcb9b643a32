variables:
  - name: workingDirectoryTerraform # This variable allows managing different configurations in the same repository
    value: $(System.DefaultWorkingDirectory)/iac/terraform
  - group: common-variables
  - group: frontend

trigger:
  batch: true
  branches:
    include:
    - Main

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: ExecuteTerraform
  displayName: Execute Terraform
  jobs:
  - job: TerraformPlan
    displayName: Terraform plan
    continueOnError: false
    steps:
    - task: TerraformInstaller@1
      displayName: Install Terraform
      inputs:
        terraformVersion: $(terraformVersion)

    - task: TerraformTaskV4@4
      displayName: Initialize Terraform
      inputs:
        provider: 'azurerm'
        command: 'init'
        workingDirectory: $(workingDirectoryTerraform)
        backendServiceArm: $(serviceConnection)
        backendAzureRmResourceGroupName: $(tfBackendStorageAccountResourceGroup)
        backendAzureRmStorageAccountName: $(tfBackendStorageAccountName)
        backendAzureRmContainerName: $(tfBackendStorageAccountContainerName)
        backendAzureRmKey: $(tfBackendStorageAccountKey)

    - task: TerraformTaskV4@4
      displayName: Validate Terraform configuration
      inputs:
        provider: 'azurerm'
        command: 'validate'
        workingDirectory: $(workingDirectoryTerraform)
        environmentServiceNameAzureRM: $(serviceConnection)

    - task: TerraformTaskV4@4
      name: terraformPlan
      displayName: Create Terraform plan
      inputs:
        provider: 'azurerm'
        command: 'plan'
        commandOptions: '-out plan.tfplan'
        workingDirectory: $(workingDirectoryTerraform)
        environmentServiceNameAzureRM: $(serviceConnection)

    # Plan must be saved between job execution since they might take place on different agents
    - task: PublishPipelineArtifact@1
      inputs:
        targetPath: $(workingDirectoryTerraform)/plan.tfplan
        artifactName: plan.tfplan

  # ManualValidation task is used to allow reviewing the Terraform plan before applying it. It must be
  # placed in a separate job since it's an agent-less job.
  - job: Validate
    displayName: Manual plan validation
    condition: and(succeeded(), eq(dependencies.TerraformPlan.outputs['terraformPlan.changesPresent'], 'true'))
    dependsOn: TerraformPlan
    continueOnError: false
    pool: server
    steps:
    - task: ManualValidation@0
      displayName: Validate Terraform plan
      timeoutInMinutes: 60 # task times out in 1 day
      inputs:
        instructions: 'Please validate the Terraform plan and resume afterwards'

  - job: TerraformApply
    displayName: Terraform apply
    condition: and(succeeded(), eq(dependencies.TerraformPlan.outputs['terraformPlan.changesPresent'], 'true'))
    dependsOn: Validate
    continueOnError: false
    steps:
    - task: DownloadPipelineArtifact@2
      inputs:
        path: $(workingDirectoryTerraform)
        artifact: plan.tfplan

    - task: TerraformInstaller@1
      displayName: Install Terraform
      inputs:
        terraformVersion: $(terraformVersion)

    - task: TerraformTaskV4@4
      displayName: Initialize Terraform
      inputs:
        provider: 'azurerm'
        command: 'init'
        workingDirectory: $(workingDirectoryTerraform)
        backendServiceArm: $(serviceConnection)
        backendAzureRmResourceGroupName: $(tfBackendStorageAccountResourceGroup)
        backendAzureRmStorageAccountName: $(tfBackendStorageAccountName)
        backendAzureRmContainerName: $(tfBackendStorageAccountContainerName)
        backendAzureRmKey: $(tfBackendStorageAccountKey)

    - task: TerraformTaskV4@4
      displayName: Apply Terraform plan
      inputs:
        provider: 'azurerm'
        command: 'apply'
        commandOptions: '-auto-approve plan.tfplan'
        workingDirectory: $(workingDirectoryTerraform)
        environmentServiceNameAzureRM: $(serviceConnection)

- stage: DeployPortal
  displayName: Deploy Portal
  dependsOn: ExecuteTerraform
  jobs:
  - job: DeployStaticWebApp
    displayName: Deploy Static Web App
    continueOnError: false
    steps:
    - task: AzureStaticWebApp@0
      inputs:
        azure_static_web_apps_api_token: $(staticWebAppDeploymentToken)
        skip_api_build: true
        app_location: "/" # App source code path
        output_location: "dist/ai-tender-analysis/browser"
