// @ts-check
const eslint = require('@eslint/js')
const tseslint = require('typescript-eslint')
const angular = require('angular-eslint')
const prettier = require('eslint-plugin-prettier')
const prettierConfig = require('eslint-config-prettier')
const simpleImportSort = require('eslint-plugin-simple-import-sort')

module.exports = tseslint.config(
    {
        files: ['**/*.ts'],
        ignores: ['src/app/api/**/*'],
        extends: [
            eslint.configs.recommended,
            ...tseslint.configs.recommended,
            ...tseslint.configs.stylistic,
            ...angular.configs.tsRecommended,
            prettierConfig,
        ],
        processor: angular.processInlineTemplates,
        plugins: { prettier, 'simple-import-sort': simpleImportSort },
        rules: {
            '@angular-eslint/directive-selector': [
                'error',
                {
                    type: 'attribute',
                    prefix: 'app',
                    style: 'camelCase',
                },
            ],
            '@angular-eslint/component-selector': [
                'error',
                {
                    type: 'element',
                    prefix: 'app',
                    style: 'kebab-case',
                },
            ],
            'prettier/prettier': 'error',
            'simple-import-sort/imports': [
                'error',
                {
                    groups: [
                        // 1. Side effect imports
                        ['^\\u0000'],
                        // 2. Angular imports
                        ['^@angular'],
                        // 3. App-specific imports
                        [
                            '^@app/',
                            '^@environments/',
                            '^@core/',
                            '^@modules/',
                            '^@shared/',
                        ],
                        // 4. Relative imports
                        ['^\\./', '^\\.\\./'],
                        // 5. Styles and assets
                        ['^.+\\.(module.scss|scss|gif|png|svg|jpg|jpeg)$'],
                    ],
                },
            ],
            'no-console': ['error', { allow: ['warn', 'error'] }],
            '@angular-eslint/no-empty-lifecycle-method': 'off',
            '@typescript-eslint/no-explicit-any': 'off',
        },
    },
    {
        files: ['**/*.html'],
        extends: [
            ...angular.configs.templateRecommended,
            ...angular.configs.templateAccessibility,
        ],
        rules: {
            '@angular-eslint/template/prefer-control-flow': 'error',
            '@angular-eslint/template/no-duplicate-attributes': 'error',
            '@angular-eslint/template/no-negated-async': 'error',
            '@angular-eslint/template/button-has-type': 'warn',
        },
    }
)
