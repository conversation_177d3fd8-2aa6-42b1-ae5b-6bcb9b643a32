locals {
  project_name                     = "aita"
  rg_name_general                  = "rg-${local.project_name}-general"
  region_name_germany_west_central = "Germany West Central"
  tags = {
    "Business Unit" = "Gofore DACH"
    "Project Name"  = "AI Tender Analysis"
    "Managed by"    = "Terraform"
  }
}

module "resource_group_portal" {
  source   = "./modules/resourcegroup"
  name     = "${local.project_name}-portal"
  location = local.region_name_germany_west_central

  tags = local.tags
}

