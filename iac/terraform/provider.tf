terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.16.0"
    }
  }

  required_version = ">= 1.10.0"

  backend "azurerm" {
    resource_group_name  = "rg-aita-general"
    storage_account_name = "staaitatf"
    container_name       = "terraform-state"
    # Important: The key must be unique for every different Terraform configuration and state. Terraform workspaces
    # automatically create different state files with the following pattern: <key>:<workspace>
    key                  = "terraform_frontend.tfstate"
  }
}

provider "azurerm" {
  features {}
  subscription_id = "899c65d3-80bf-4839-ab42-ccf83907c948"
}
