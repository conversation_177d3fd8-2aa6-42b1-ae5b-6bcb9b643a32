data "azurerm_key_vault" "general" {
  name                = "kv-aita-general"
  resource_group_name = local.rg_name_general
}

data "azurerm_key_vault_secret" "app_registration_client_id" {
  name         = "app-registration-client-id"
  key_vault_id = data.azurerm_key_vault.general.id
}

data "azurerm_key_vault_secret" "app_registration_client_secret" {
  name         = "app-registration-client-secret-spa"
  key_vault_id = data.azurerm_key_vault.general.id
}

resource "azurerm_static_web_app" "portal" {
  name                         = "stapp-${local.project_name}-portal"
  resource_group_name          = module.resource_group_portal.name
  location = "westeurope" # Currently, only available in West Europe
  sku_tier                     = "Standard"
  sku_size                     = "Standard"
  preview_environments_enabled = false

  app_settings = {
    AZURE_CLIENT_ID     = data.azurerm_key_vault_secret.app_registration_client_id.value
    AZURE_CLIENT_SECRET = data.azurerm_key_vault_secret.app_registration_client_secret.value
  }

  tags = local.tags

  # These values seem to change dynamically depending on recent deployment and we don't want to configure it manually
  # in TF
  lifecycle {
    ignore_changes = [
      repository_branch,
      repository_url
    ]
  }
}

resource "azurerm_key_vault_secret" "swa_portal_hostname" {
  name         = "static-web-app-portal-hostname"
  value        = azurerm_static_web_app.portal.default_host_name
  key_vault_id = data.azurerm_key_vault.general.id
}

resource "azurerm_key_vault_secret" "swa_portal_api_key" {
  name         = "static-web-app-portal-api-key"
  value        = azurerm_static_web_app.portal.api_key
  key_vault_id = data.azurerm_key_vault.general.id
}
