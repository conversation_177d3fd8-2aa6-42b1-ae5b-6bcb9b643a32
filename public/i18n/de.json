{"paginator": {"rangePageLabelEmpty": "0 von {{length}}", "rangePageLabel": "{{start}} – {{end}} von {{length}}", "itemsPerPageLabel": "Einträge pro Seite", "nextPageLabel": "Nächste Seite", "previousPageLabel": "Vorherige Seite", "firstPageLabel": "Erste Seite", "lastPageLabel": "Letzte Seite"}, "confirm-dialog": {"unsaved-changes": {"header": "Nicht gespeicherte Änderungen", "text": "Es gibt nicht gespeicherte Änderungen. Sind Si<PERSON> sicher, dass Sie diese verwerfen und fortfahren möchten?"}, "default": {"header": "Fortfahren?", "text": "Wollen Sie wirklich mit dieser Aktion fortfahren?", "no": "<PERSON><PERSON>", "yes": "<PERSON>a"}, "delete-document": {"header": "Dokument löschen", "text": "Wollen Sie dieses Dokument wirklich löschen?"}, "analyze-tender": {"header": "Neue Analyse starten", "text": "Wollen Sie wirklich eine neue Analyse starten? Das bestehende Analyseergebnis wird dadurch gelöscht."}, "delete-tender": {"header": "Ausschreibung löschen", "text": "Wollen Sie diese Ausschreibung wirklich löschen?"}, "delete-files": {"header": "<PERSON><PERSON>", "text": "Wollen Sie diese {{length}} <PERSON><PERSON> wirklich löschen?"}, "delete-file": {"header": "<PERSON><PERSON>", "text": "Wollen Sie diese Datei wirklich löschen?"}}, "dialog": {"default": {"input-header": "Eingabe erforderlich", "input-text": "<PERSON>te geben Si<PERSON> den erforderlichen Wert ein.", "input-label": "Wert", "cancel-button": "Abbrechen", "ok-button": "Ok"}, "url": {"input-header": "<PERSON><PERSON>", "input-text": "<PERSON>te geben Sie die Url für den Link ein.", "input-label": "URL"}}, "error": {"required-field": {"error-message-default": "<PERSON>ld muss ausgefüllt sein."}, "pattern-field": {"error-message-datetime": "Falsches Datum-Zeit-Format.", "error-message-pattern": "Feldwert entspricht nicht der Vorgabe.", "error-message-date": "Falsches Datum-Format.", "error-message-number": "Feldwert akzeptiert nur Zahlen.", "error-message-url": "Falsches URL Format. Bitte inklusive 'http(s)://' eingeben."}, "field": {"error-message-default": "Feld hat einen <PERSON>hler."}}, "popup": {"error": {"fetch-system-config": "Systemkonfiguration konnte nicht geladen werden.", "update-system-config": "Systemkonfiguration konnte nicht aktualisiert werden.", "fetch-tender": "Ausschreibung wurde nicht gefunden.", "update-tender": "Die Ausschreibung konnte nicht aktualisiert werden.", "create-tender": "Die neue Ausschreibung konnte nicht angelegt werden.", "invalid-file-type": "<PERSON><PERSON>p nicht erlaubt.", "duplicated-file-tender": "Datei bereits hochgeladen.", "delete-file-tender": "Die Datei konnte nicht gelöscht werden.", "add-file-tender": "Die Datei konnte nicht hochgeladen werden.", "download-file-tender": "Die Datei konnte nicht heruntergeladen werden.", "download-files-zip": "Die Dateien konnten nicht als zip Datei heruntergeladen werden.", "update-tender-description": "Aktualisierung der Beschreibung der Ausschreibung nicht erfolgreich.", "fetch-tender-description": "Die Beschreibung der Ausschreibung konnte nicht gefunden werden.", "analyze-tender": "Ausschreibung konnte nicht analysiert werden.", "fetch-tenders": "Ausschreibungen konnten nicht geladen werden.", "delete-tender": "Die Ausschreibung konnte nicht gelöscht werden.", "message": "<PERSON><PERSON>:"}, "success": {"update-system-config": "Systemkonfiguration erfolgreich aktualisiert.", "update-tender": "Ausschreibung wurde erfolgreich aktualisiert.", "create-tender": "Neue Ausschreibung wurde erfolgreich angelegt.", "download-file": "<PERSON>i wurde erfolgreich heruntergeladen.", "download-files-zip": "<PERSON><PERSON> wurden erfolgreich als zip Datei heruntergeladen.", "update-tender-description": "Die Beschreibung der Ausschreibung wurde erfolgreich aktualisiert.", "delete-tender": "Die Ausschreibung wurde gelöscht.", "message": "Erfolg:"}}, "tender": {"edit-modal": {"title": "Ausschreibung bearbeiten", "edit-mode-off-button": "<PERSON><PERSON><PERSON>", "edit-mode-on-button": "Editieren", "analytics-mode-off-button": "Details", "analytics-mode-on-button": "Analyse"}, "details": {"analysis": {"title": "Ausschreibung analysieren"}, "title": "Ausschreibungsdetails", "name-input": {"label": "Titel", "placeholder": "Ausschreibungstitel"}, "client": {"label": "Auftraggeber", "placeholder": "Beispiel GmbH"}, "sourceUrl": {"label": "<PERSON><PERSON>", "placeholder": "https://example.com", "hint": "Direkter Link zur Veröffentlichung der Ausschreibung."}, "publicationDate": {"label": "Veröffentlichungsdatum"}, "submissionDate": {"label": "A<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Genauer Zeitpunkt bis zu welchem das Abgabepaket gesamt abzuliefern ist."}, "contractDuration": {"label": "Auftragsdauer / Laufzeit", "placeholder": "x Jahre"}, "contractValue": {"label": "Auftragswert", "placeholder": "100000", "hint": "Der Auftragswert wird in € angegeben."}, "maximumBudget": {"label": "Maximales Abrufvolumen", "placeholder": "500 PT", "hint": "<PERSON> Maximalbetrag, gelistet in Personentagen (PT)."}, "deliveryLocation": {"label": "Erfüllungsort", "placeholder": "Salzburg"}, "winningCriteria": {"label": "Zuschlagskriterien", "placeholder": "Zertifikate, etc.", "hint": "<PERSON><PERSON> von Kriterien, die besonders wesentlich zur Vergabe sind."}, "weightingPriceQuality": {"label": "Gewichtung Preis / Qualität", "placeholder": "50% / 50%", "hint": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> Pre<PERSON> zu Qualität."}, "questionDeadline": {"label": "Fragendeadline"}, "bindingDeadline": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Die Bindefrist eines Angebots ist die Zeitspanne, in der ein:e Bieter:in zivilrechtlich an sein oder ihr Angebot gebunden ist. Innerhalb der Bindefrist darf das Angebot nicht geändert oder zurückgezogen werden."}, "comment": {"placeholder": "Schreiben Sie ein Kommentar zu dieser Ausschreibung.", "label": "Kommentar"}, "rating": {"label": "Rating"}, "isFavorite": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "created-at": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "modified-at": {"label": "Zuletzt geändert"}, "bidWorthy": {"label": "Angebotswürdig"}, "created-by": {"label": "von "}, "modified-by": {"label": "von "}, "header": "Details"}, "config": {"label": "Systemkonfiguration", "aiAnalysisSystemPrompt-input": {"label": "System-Prompt"}, "aiAnalysisAnalysisPrompt-input": {"label": "Analyse-Prompt"}}, "description": {"description-input": {"placeholder": "Beschreibung", "label": "Beschreibung"}, "header": "Beschreibung"}, "create-modal": {"title": "Ausschreibung erstellen", "ai-mode-off-button": "Wechseln zum manuellen Anlegen", "ai-mode-on-button": "Wechseln zum KI-gestützten Anlegen"}, "documents": {"header": "Dokumente", "table": {"name": "Name", "size": "Größe", "created-at": "Erstellt am", "format": "Format", "actions": "Aktionen"}, "drag-and-drop-files-here": "<PERSON><PERSON><PERSON> Sie Ihre Dateien in das Feld oder klicken Sie um Dateien hinzuzufügen.", "delete-selected-files": "Ausgewählte Dateien löschen", "download-all-files-as-zip": "Ausgewählte Dateien als zip herunterladen", "uploaded-files-title": "Hochgeladene Dateien", "select-all": "Alle auswählen", "new-files-title": "Neue Dateien", "search": {"label": "<PERSON><PERSON>", "placeholder": "Suchen..."}, "add-button": "<PERSON><PERSON>", "error": {"too-large": "Datei ist zu groß - Höchstgröße: 10MB - Achtung: wird nicht hochgeladen!"}, "too-large-label": "<PERSON>r<PERSON>ße!", "upload-all-selected-files": "Ausgewählte Dateien hochladen", "hint": {"file-delete-error": "Datei konnte nicht gelöscht werden.", "file-download-error": "<PERSON>i konnte nicht heruntergeladen werden.", "file-upload-error": "Datei konnte nicht hochgeladen werden."}}, "analysis": {"result": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "analyze-tender": {"hint": "Das Ergebnis beruht auf einer KI-gestützten Analyse der Ausschreibungsbeschreibung und aller hochgeladenen Dateien."}, "prompt-tokens": {"label": "Verwendete Prompt-Token <PERSON>"}, "completion-tokens": {"label": "Verwendete Completion-Token <PERSON>hl"}, "tender-description": {"label": "Beschreibung der Ausschreibung (Basis der Analyse)"}}, "table": {"title": "Titel", "created-at": "Erstellt am", "last-modified": "Zuletzt geändert", "contract-value": "Vertragswert", "actions": "Aktionen", "created-by": "<PERSON><PERSON><PERSON><PERSON> von", "description": "Beschreibung", "documents": "Dokumente", "link": "Link"}, "title": "Neue Ausschreibung"}, "modal": {"cancel-button": "Abbrechen", "save-button": "Speichern", "analyze": {"analysis-in-progress": "Analyse wird gerade durchgeführt."}, "analyze-button": "Starte Analyse"}, "button": {"default": {"hint": {"copy-markdown": "<PERSON><PERSON><PERSON> M<PERSON>", "copy-preview": "<PERSON><PERSON><PERSON>", "copy-report": "<PERSON><PERSON><PERSON>"}}}, "md - editor": {"default": {"preview": "Vorschau"}}, "base-header": {"title": "AI supported Tender Analysis", "link": {"all-tenders": "Alle Ausschreibungen", "example-page": "Beispielseite"}}, "system-configuration": {"prompts": {"header": "Prompts", "system-prompt-section": {"header": "System-Prompt", "textarea": {"placeholder": "Du bist ein Experte in..."}}, "analysis-prompt-section": {"header": "Analyse-Prompt", "textarea": {"placeholder": "Analysiere alle Dokumente..."}}, "structured-output-prompt-section": {"header": "Structured-Output-Prompt", "textarea": {"placeholder": "Du bist ein Ausschreibungs-Extraktionsdienst, der ausschließlich ein einzelnes JSON-Objekt ausgibt…"}}}, "all-tenders-component": {"all-tenders-header": "Alle Ausschreibungen", "input-filter-placeholder": "<PERSON><PERSON>", "add-button": "Neue Ausschreibung hinzufügen"}, "common": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "table": {"no-data": {"message": "Es konnten keine Einträge gefunden werden."}}, "login-component": {"log-in-button": "Einloggen"}, "datetimepicker-field": {"error-message-default": "Ein Datum muss ausgewählt sein!"}}, "datetimepicker": {"date": {"placeholder": "<PERSON><PERSON> w<PERSON>hlen"}, "datetime": {"placeholder": "Zeit wählen"}}, "menu-modal": {"open-button": {"aria-label": "<PERSON><PERSON>"}, "switch-language-title": "Gewählte Sprache:", "log-out-button": "Abmelden", "prompts-link": "Prompts"}, "all-tenders-component": {"all-tenders-header": "Alle Ausschreibungen", "input-filter-placeholder": "<PERSON><PERSON>", "add-button": "Neue Ausschreibung hinzufügen"}, "common": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "table": {"no-data": {"message": "<PERSON>s wurden keine Einträge gefunden."}}, "login-component": {"log-in-button": "Log in"}, "md-toolbar": {"default": {"hint": {"bold": "fett", "italic": "kursiv", "strikethrough": "durchgestrichen", "code": "Code", "heading1": "Überschrift 1", "heading2": "Überschrift 2", "heading3": "Überschrift 3", "quote": "Zitat", "unordered_list": "Aufzählung", "ordered_list": "nummerierte Liste", "link": "Link", "table": "<PERSON><PERSON><PERSON>", "horizontal_rule": "horizontale Linie"}}}}