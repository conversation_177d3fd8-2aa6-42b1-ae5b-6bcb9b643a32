{"paginator": {"rangePageLabelEmpty": "0 of {{length}}", "rangePageLabel": "{{start}} – {{end}} of {{length}}", "itemsPerPageLabel": "Items per page", "nextPageLabel": "Next", "previousPageLabel": "Previous", "firstPageLabel": "First page", "lastPageLabel": "Last page"}, "confirm-dialog": {"unsaved-changes": {"header": "Unsaved changes", "text": "You have unsaved changes. Are you sure you want to discard them and continue?"}, "default": {"header": "Continue?", "text": "Do you really want to continue?", "no": "No", "yes": "Yes"}, "delete-document": {"header": "Delete document", "text": "Do you really want to delete the document?"}, "analyze-tender": {"header": "Start New Analysis", "text": "Do you really want to start a new analysis? The existing analysis result will be deleted."}, "delete-tender": {"header": "Delete tender", "text": "Do you really want to delete the tender?"}, "delete-files": {"header": "Delete files", "text": "Do you really want to delete this {{length}} files?"}, "delete-file": {"header": "Delete file", "text": "Do you really want to delete this file?"}}, "dialog": {"default": {"input-header": "Input required", "input-text": "Please enter the required value.", "input-label": "Value", "cancel-button": "Cancel", "ok-button": "OK"}, "url": {"input-header": "URL Input", "input-text": "Please enter the URL for the link.", "input-label": "URL"}}, "error": {"required-field": {"error-message-default": "Field must be filled out."}, "pattern-field": {"error-message-datetime": "Incorrect date-time format.", "error-message-pattern": "Field value does not match the specification.", "error-message-date": "Incorrect date format.", "error-message-number": "Field value accepts only numbers.", "error-message-url": "Incorrect URL format. Please include 'http(s)://' in the URL."}, "field": {"error-message-default": "<PERSON> has an error."}}, "popup": {"error": {"fetch-system-config": "System configuration could not be loaded.", "update-system-config": "System configuration could not be updated.", "fetch-tender": "<PERSON>der was not found.", "update-tender": "Tender could not be updated.", "create-tender": "The new tender could not be created.", "invalid-file-type": "File has invalid type.", "duplicated-file-tender": "Duplicated file.", "delete-file-tender": "Could not delete file.", "add-file-tender": "Could not add file.", "download-file-tender": "Could not download file.", "download-files-zip": "Could not download files as zip file.", "update-tender-description": "Tender description update was not successful.", "fetch-tender-description": "Tender description could not be found.", "analyze-tender": "Tender could not be analyzed.", "fetch-tenders": "Tenders could not be loaded.", "delete-tender": "Tender could not be deleted.", "message": "Error:"}, "success": {"update-system-config": "System configuration successfully updated.", "update-tender": "Tender was updated successfully.", "create-tender": "New tender was created successfully.", "download-file": "Files successfully downloaded.", "download-files-zip": "Files successfully downloaded as zip file.", "update-tender-description": "Tender description was updated successfully.", "delete-tender": "<PERSON><PERSON> successfully deleted.", "message": "Success:"}}, "tender": {"edit-modal": {"title": "Edit tender", "edit-mode-off-button": "Stop editing", "edit-mode-on-button": "Edit", "analytics-mode-off-button": "Details", "analytics-mode-on-button": "Analysis"}, "details": {"analysis": {"title": "Analyze tender"}, "title": "Tenderdetails", "name-input": {"label": "Title", "placeholder": "Tender title"}, "client": {"label": "Client", "placeholder": "Example Ltd."}, "sourceUrl": {"label": "Source URL", "placeholder": "https://example.com", "hint": "Direct link to the original source of the tender publication."}, "publicationDate": {"label": "Publication date"}, "submissionDate": {"label": "Submission date", "hint": "Exact date by which the entire delivery package must be delivered."}, "contractDuration": {"label": "Contract duration", "placeholder": "x years"}, "contractValue": {"label": "Contract value", "placeholder": "100000", "hint": "The contract value is stated in €."}, "maximumBudget": {"label": "Maximum budget", "placeholder": "500 PT", "hint": "The maximum amount listed in person-days (PT)."}, "deliveryLocation": {"label": "Delivery location", "placeholder": "Salzburg"}, "winningCriteria": {"label": "Winning criteria", "placeholder": "Certificates, etc.", "hint": "List of criteria that are especially crucial for awarding the contract."}, "weightingPriceQuality": {"label": "Weighting price/quality", "placeholder": "50% / 50%", "hint": "Weighting of price versus quality."}, "questionDeadline": {"label": "Question deadline"}, "bindingDeadline": {"label": "Binding deadline", "hint": "The binding deadline is the period during which a bidder is legally bound by their offer. Within this period, the offer cannot be modified or withdrawn."}, "comment": {"placeholder": "Leave a comment for this tender.", "label": "Comment"}, "rating": {"label": "Rating"}, "isFavorite": {"label": "Favorite"}, "created-at": {"label": "Created"}, "modified-at": {"label": "Last modified"}, "bidWorthy": {"label": "Bid-worthy"}, "created-by": {"label": "by "}, "modified-by": {"label": "by "}, "header": "Details"}, "config": {"label": "System configuration", "aiAnalysisSystemPrompt-input": {"label": "System prompt"}, "aiAnalysisAnalysisPrompt-input": {"label": "Analysis prompt"}}, "description": {"description-input": {"placeholder": "Description", "label": "Description"}, "header": "Description"}, "create-modal": {"title": "C<PERSON> Tender", "ai-mode-off-button": "Switch to manual creation", "ai-mode-on-button": "Switch to AI-assisted creation"}, "documents": {"header": "Documents", "table": {"name": "Name", "size": "Size", "created-at": "Created on", "format": "Format", "actions": "Actions"}, "drag-and-drop-files-here": "Drag & drop files here or click to add files.", "delete-selected-files": "Delete selected files", "download-all-files-as-zip": "Download selected files as zip", "uploaded-files-title": "Uploaded files", "select-all": "Select all", "new-files-title": "New files", "search": {"label": "Search", "placeholder": "Search..."}, "add-button": "Add files", "error": {"too-large": "File is too large – Maximum size: 10 MB – Warning: will not be uploaded!"}, "too-large-label": "Filesize warning", "upload-all-selected-files": "Upload all selected files", "hint": {"file-delete-error": "File could not be deleted.", "file-download-error": "File could not be downloaded.", "file-upload-error": "File could not be uploaded."}}, "analysis": {"result": {"label": "Result"}, "analyze-tender": {"hint": "The result is based on an AI-assisted analysis of the tender description and all uploaded files."}, "prompt-tokens": {"label": "Number of prompt tokens used"}, "completion-tokens": {"label": "Number of completion tokens used"}, "tender-description": {"label": "Tender description (basis for the analysis)"}}, "table": {"title": "Title", "created-at": "Created on", "last-modified": "Last modified", "contract-value": "Contract value", "actions": "Actions", "created-by": "Created by", "description": "Description", "documents": "Documents", "link": "Link"}, "title": "New Tender"}, "modal": {"cancel-button": "Cancel", "save-button": "Save", "analyze": {"analysis-in-progress": "Analysis in progress."}, "analyze-button": "Start analysis"}, "button": {"default": {"hint": {"copy-markdown": "Copy md text", "copy-preview": "Copy preview", "copy-report": "Copy analxsis"}}}, "md - editor": {"default": {"preview": "Preview"}}, "base-header": {"title": "AI supported Tender Analysis", "link": {"all-tenders": "All tenders", "example-page": "Example page"}}, "system-configuration": {"prompts": {"header": "Prompts", "system-prompt-section": {"header": "System Prompt", "textarea": {"placeholder": "You are an expert in..."}}, "analysis-prompt-section": {"header": "Analysis Prompt", "textarea": {"placeholder": "Analyze all documents..."}}, "structured-output-prompt-section": {"header": "Structured Output Prompt", "textarea": {"placeholder": "You are a tender-extraction service that outputs only a single JSON object..."}}}}, "datetimepicker": {"date": {"placeholder": "Select date"}, "datetime": {"placeholder": "Select time"}}, "menu-modal": {"open-button": {"aria-label": "Open menu"}, "switch-language-title": "Content language:", "log-out-button": "Log out", "prompts-link": "Prompts"}, "all-tenders-component": {"all-tenders-header": "All tenders", "input-filter-placeholder": "Search", "add-button": "Add new tender"}, "common": {"yes": "Yes", "no": "No"}, "table": {"no-data": {"message": "No entries could be found"}}, "login-component": {"log-in-button": "<PERSON><PERSON>"}, "md-toolbar": {"default": {"hint": {"bold": "bold", "italic": "italic", "strikethrough": "strikethrough", "code": "code", "heading1": "heading 1", "heading2": "heading 2", "heading3": "heading 3", "quote": "quote", "unordered_list": "unordered list", "ordered_list": "ordered list", "link": "link", "table": "table", "horizontal_rule": "horizontal rule"}}}, "datetimepicker-field": {"error-message-default": "A date must be selected!"}}