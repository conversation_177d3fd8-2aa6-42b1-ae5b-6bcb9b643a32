import { readFile, writeFile } from 'fs/promises'
import { unflatten } from 'flat'
import path from 'path'
import { fileURLToPath } from 'url'

const languages = ['en', 'de', 'fi']
const i18nDir = path.join(
    path.dirname(fileURLToPath(import.meta.url)),
    '../public/i18n'
)

;(async () => {
    try {
        for (const lang of languages) {
            const filePath = path.join(i18nDir, `${lang}.json`)

            const fileExists = await readFile(filePath, 'utf8').catch(
                () => null
            )
            if (fileExists) {
                const flatData = JSON.parse(fileExists)
                const nestedData = unflatten(flatData, {
                    safe: true,
                })

                await writeFile(
                    filePath,
                    JSON.stringify(nestedData, null, 4),
                    'utf8'
                )
                console.log(`Converted ${lang}.json to nested format`)
            }
        }
    } catch (error) {
        console.error('Error processing i18n files:', error)
    }
})()
