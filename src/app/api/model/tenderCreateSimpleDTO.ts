/**
 * AI Tender Analysis - REST API
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WorkflowStatusDTO } from './workflowStatusDTO'

/**
 * Simplified tender creation DTO for AI-assisted tender creation with only essential fields
 */
export interface TenderCreateSimpleDTO {
    /**
     * Title of the tender
     */
    title: string
    /**
     * Direct link to the original source of the tender
     */
    sourceUrl?: string
    /**
     * Description text that will be analyzed by AI to extract missing fields
     */
    description: string
    /**
     * List of documents that will be analyzed to extract tender information
     */
    files?: Array<Blob>
    workflowStatus?: WorkflowStatusDTO
}
export namespace TenderCreateSimpleDTO {}
