{"name": "@gofore.aita/api", "version": "1.0.0", "description": "OpenAPI client for @gofore.aita/api", "author": "OpenAPI-Generator Contributors", "repository": {"type": "git", "url": "https://github.com/GIT_USER_ID/GIT_REPO_ID.git"}, "keywords": ["openapi-client", "openapi-generator"], "license": "Unlicense", "scripts": {"build": "ng-packagr -p ng-package.json"}, "peerDependencies": {"@angular/core": "^19.0.0", "rxjs": "^7.4.0"}, "devDependencies": {"@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/core": "^19.0.0", "@angular/platform-browser": "^19.0.0", "ng-packagr": "^19.0.0", "reflect-metadata": "^0.1.3", "rxjs": "^7.4.0", "typescript": ">=5.5.0 <5.7.0", "zone.js": "^0.15.0"}}