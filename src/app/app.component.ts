import { Component, OnInit } from '@angular/core'
import { MatIconRegistry } from '@angular/material/icon'
import { DomSanitizer } from '@angular/platform-browser'
import {
    NavigationEnd,
    Router,
    RouterModule,
    RouterOutlet,
} from '@angular/router'

import { LOGIN_URL, RETURN_URL_KEY } from '@core/auth/auth.constants'

import { MsalModule } from '@azure/msal-angular'
import { TranslateModule } from '@ngx-translate/core'
import { filter } from 'rxjs'

@Component({
    selector: 'app-root',
    imports: [RouterOutlet, RouterModule, MsalModule, TranslateModule],
    templateUrl: './app.component.html',
    standalone: true,
    styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
    constructor(
        private router: Router,
        matIconRegistry: MatIconRegistry,
        domSanitizer: DomSanitizer
    ) {
        matIconRegistry.addSvgIconSet(
            domSanitizer.bypassSecurityTrustResourceUrl('assets/mdi.svg')
        )
    }

    ngOnInit(): void {
        // stores current route as returnUrl for unintentional signOut
        this.router.events
            .pipe(filter((event) => event instanceof NavigationEnd))
            .subscribe((event: NavigationEnd) => {
                const currentUrl = event.urlAfterRedirects
                if (currentUrl !== LOGIN_URL) {
                    localStorage.setItem(RETURN_URL_KEY, currentUrl)
                }
            })
    }
}
