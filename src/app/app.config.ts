import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common'
import {
    HTTP_INTERCEPTORS,
    HttpClient,
    provideHttpClient,
    withFetch,
    withInterceptors,
} from '@angular/common/http'
import {
    ApplicationConfig,
    importProvidersFrom,
    inject,
    LOCALE_ID,
    provideAppInitializer,
    provideZoneChangeDetection,
    SecurityContext,
} from '@angular/core'
import {
    DateAdapter,
    MAT_DATE_FORMATS,
    MAT_DATE_LOCALE,
    NativeDateAdapter,
} from '@angular/material/core'
import { MatPaginatorIntl } from '@angular/material/paginator'
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async'
import { provideRouter, withRouterConfig } from '@angular/router'

import { BASE_PATH } from '@app/api'
import { environment } from '@environments/environment'
import { AuthService } from '@core/auth/auth.service'
import { MsalGuardConfigFactory } from '@core/auth/msal/msal.guard.config'
import { MsalInstanceFactory } from '@core/auth/msal/msal.instance.config'
import { MsalInterceptorConfigFactory } from '@core/auth/msal/msal.interceptor.config'
import { msalInterceptorAdapter } from '@core/auth/msal/msal-interceptor-adapter'
import { CustomPaginatorIntl } from '@shared/custom-mat-paginator-intl'
import { I18nService } from '@shared/services/i18n.service'
import { LocaleCurrencyPipe } from '@shared/utils/custom-pipes/locale-currency.pipe'
import { LocaleDatePipe } from '@shared/utils/custom-pipes/locale-date.pipe'
import { LocaleDecimalPipe } from '@shared/utils/custom-pipes/locale-decimal.pipe'
import { StarIconsPipe } from '@shared/utils/custom-pipes/star-icons.pipe'

import { routes } from './app.routes'

import {
    MSAL_GUARD_CONFIG,
    MSAL_INSTANCE,
    MSAL_INTERCEPTOR_CONFIG,
    MsalBroadcastService,
    MsalGuard,
    MsalInterceptor,
    MsalService,
} from '@azure/msal-angular'
import { TranslateLoader, TranslateModule } from '@ngx-translate/core'
import { TranslateHttpLoader } from '@ngx-translate/http-loader'
import { MARKED_OPTIONS, provideMarkdown } from 'ngx-markdown'

const httpLoaderFactory: (http: HttpClient) => TranslateHttpLoader = (
    http: HttpClient
) => new TranslateHttpLoader(http, './i18n/', '.json')

const i18nInitializerFactory = () => {
    const languageService = inject(I18nService)
    languageService.init()
}

const authInitializerFactory = () => {
    const authService = inject(AuthService)
    authService.init()
}

export const CUSTOM_DATE_FORMATS = {
    parse: {
        dateInput: 'LL',
        timeInput: 'LT',
    },
    display: {
        dateInput: 'LL',
        monthYearLabel: 'MMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
        timeInput: 'LT',
        timeOptionLabel: 'LT',
    },
}

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(
            routes,
            withRouterConfig({
                onSameUrlNavigation: 'reload',
            })
        ),
        provideAnimationsAsync(),
        provideHttpClient(
            withFetch(),
            withInterceptors([msalInterceptorAdapter])
        ),
        { provide: MatPaginatorIntl, useClass: CustomPaginatorIntl },
        { provide: BASE_PATH, useValue: environment.apiUrl },
        { provide: LOCALE_ID, useValue: 'de-DE' },
        { provide: MAT_DATE_LOCALE, useValue: 'de-DE' },
        { provide: MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
        {
            provide: DateAdapter,
            useClass: NativeDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
        importProvidersFrom(CommonModule),
        DecimalPipe,
        CurrencyPipe,
        LocaleDatePipe,
        LocaleCurrencyPipe,
        LocaleDecimalPipe,
        StarIconsPipe,
        {
            provide: HTTP_INTERCEPTORS,
            useClass: MsalInterceptor,
            multi: true,
        },
        {
            provide: MSAL_INSTANCE,
            useFactory: MsalInstanceFactory,
        },
        {
            provide: MSAL_GUARD_CONFIG,
            useFactory: MsalGuardConfigFactory,
        },
        {
            provide: MSAL_INTERCEPTOR_CONFIG,
            useFactory: MsalInterceptorConfigFactory,
        },
        MsalService,
        MsalGuard,
        MsalBroadcastService,
        MsalInterceptor,
        provideAppInitializer(i18nInitializerFactory),
        importProvidersFrom([
            TranslateModule.forRoot({
                loader: {
                    provide: TranslateLoader,
                    useFactory: httpLoaderFactory,
                    deps: [HttpClient],
                },
            }),
        ]),
        provideAppInitializer(authInitializerFactory),
        provideMarkdown({
            sanitize: SecurityContext.NONE,

            markedOptions: {
                provide: MARKED_OPTIONS,
                useValue: {
                    gfm: true,
                    breaks: true,
                    pedantic: false,
                },
            },
        }),
    ],
}
