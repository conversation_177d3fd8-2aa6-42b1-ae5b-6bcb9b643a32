import { Routes } from '@angular/router'

import { ExampleComponent } from '@app/modules/example/example.component'
import { LoginComponent } from '@app/modules/login/pages/login.component'
import { AllTendersComponent } from '@app/modules/tenders/pages/all-tenders/all-tenders.component'
import { TenderChatComponent } from '@modules/tenders/pages/tender-chat/tender-chat.component'
import { TenderIdResolver } from '@modules/tenders/tenderResolver'
import { SystemConfigurationComponent } from '@modules/user-menu/pages/system-configuration/system-configuration.component'
import { canDeactivateGuard } from '@shared/can-deactivate-guard'
import { BaseLayoutComponent } from '@shared/components/layout/base-layout/base-layout.component'

import { MsalGuard } from '@azure/msal-angular'

export const routes: Routes = [
    {
        path: 'login',
        component: LoginComponent,
    },
    {
        path: 'tender/:tenderId',
        component: BaseLayoutComponent,
        canActivate: [MsalGuard],
        resolve: { tenderId: TenderIdResolver },

        children: [
            {
                path: 'chat',
                component: TenderChatComponent,
                canDeactivate: [canDeactivateGuard],
            },
        ],
    },
    {
        path: '',
        component: BaseLayoutComponent,
        canActivate: [MsalGuard], // only accessible for authenticated users
        children: [
            { path: 'all-tenders', component: AllTendersComponent },
            { path: 'example', component: ExampleComponent },
            { path: 'system-config', component: SystemConfigurationComponent },
            { path: '**', redirectTo: 'all-tenders' },
        ],
    },
    { path: '**', redirectTo: 'all-tenders' },
]
