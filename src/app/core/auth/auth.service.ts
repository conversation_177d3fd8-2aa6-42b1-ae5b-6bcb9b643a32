import { Injectable, signal } from '@angular/core'
import { Router } from '@angular/router'

import { environment } from '@environments/environment'
import {
    ALL_TENDERS_URL,
    LOGIN_URL,
    RETURN_URL_KEY,
} from '@core/auth/auth.constants'
import { UserDetails } from '@core/auth/user.model'
import { LoggerService } from '@core/logging/logger.service'

import { MsalService } from '@azure/msal-angular'
import {
    AccountInfo,
    AuthenticationResult,
    RedirectRequest,
} from '@azure/msal-browser'

interface IdTokenClaims {
    oid?: string
    name?: string
    preferred_username?: string
}

@Injectable({ providedIn: 'root' })
export class AuthService {
    public readonly _user = signal<UserDetails | null>(null)

    constructor(
        private msalService: MsalService,
        private router: Router,
        private logger: LoggerService
    ) {}

    init(): void {
        this.msalService.handleRedirectObservable().subscribe({
            next: (response: AuthenticationResult | null) => {
                // we received a fresh token response
                if (response && response.account) {
                    this.msalService.instance.setActiveAccount(response.account)
                    this.updateUserFromTokenResult(response)

                    const returnUrl =
                        localStorage.getItem(RETURN_URL_KEY) ?? ALL_TENDERS_URL
                    void this.router.navigateByUrl(returnUrl)
                } else {
                    // no fresh tokens came from redirect so we check if there is an account in cache
                    this.checkExistingAccount()
                }
            },
            error: (err: any) => {
                this.logger.error(
                    'Error processing redirect response from MSAL:',
                    err
                )
                void this.router.navigate([LOGIN_URL])
            },
        })
    }

    getUserDetails(): UserDetails | null {
        return this._user()
    }

    signIn(): void {
        const loginRequest: RedirectRequest = {
            scopes: environment.msal.scopes,
        }
        this.msalService.loginRedirect(loginRequest)
    }

    signOut(): void {
        const activeAccount = this.msalService.instance.getActiveAccount()
        if (!activeAccount) {
            this.logger.warn('No active account found. Redirecting to /login.')
            localStorage.removeItem(RETURN_URL_KEY)
            void this.router.navigate([LOGIN_URL])
            return
        }

        localStorage.removeItem(RETURN_URL_KEY)
        this.msalService.logoutRedirect({ account: activeAccount })
    }

    private checkExistingAccount(): void {
        const accounts = this.msalService.instance.getAllAccounts()
        if (accounts.length > 0) {
            this.msalService.instance.setActiveAccount(accounts[0])
            this.updateUserFromAccount(accounts[0])
        }
    }

    private updateUserFromTokenResult(result: AuthenticationResult): void {
        const claims = result.idTokenClaims as IdTokenClaims
        const name = claims?.name ?? ''
        const email = claims?.preferred_username ?? ''
        const id = claims?.oid ?? ''
        this._user.set({ id, name, email })
    }

    private updateUserFromAccount(account: AccountInfo): void {
        const claims = account.idTokenClaims as IdTokenClaims
        if (!claims) {
            this._user.set(null)
            return
        }
        const name = claims?.name ?? ''
        const email = claims?.preferred_username ?? ''
        const id = claims?.oid ?? 'n/a'
        this._user.set({ id, name, email })
    }
}
