import {
    <PERSON>ttp<PERSON><PERSON>,
    <PERSON>ttp<PERSON><PERSON><PERSON>,
    <PERSON>ttpHandlerFn,
    HttpInterceptorFn,
    HttpRequest,
} from '@angular/common/http'
import { inject } from '@angular/core'
import { Router } from '@angular/router'

import { LOGIN_URL } from '@core/auth/auth.constants'

import { MsalInterceptor } from '@azure/msal-angular'
import { catchError, EMPTY, Observable, throwError } from 'rxjs'

/**
 * Wraps the class-based interceptor as a functional interceptor.
 * See https://justangular.com/blog/migrate-angular-interceptors-to-function-based-interceptors "How to create a function based interceptor and how to use it?"
 * @param req
 * @param next
 */
export const msalInterceptorAdapter: HttpInterceptorFn = (
    req: HttpRequest<unknown>,
    next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
    const interceptor = inject(MsalInterceptor)
    const handler: HttpHandler = { handle: next }
    const router = inject(Router)

    return interceptor.intercept(req, handler).pipe(
        catchError((err) => {
            if (err.status === 401) {
                alert('You are not authorized, please log in')
                void router.navigate([LOGIN_URL])
                return EMPTY // Prevents further execution
            }
            return throwError(() => err)
        })
    )
}
