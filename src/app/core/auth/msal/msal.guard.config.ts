import { environment } from '@environments/environment'
import { LOGIN_URL } from '@core/auth/auth.constants'

import { MsalGuardConfiguration } from '@azure/msal-angular'
import { InteractionType } from '@azure/msal-browser'

/**
 * Configures route protection with MsalGuard.
 * @constructor
 */
export function MsalGuardConfigFactory(): MsalGuardConfiguration {
    return {
        interactionType: InteractionType.Redirect,
        authRequest: {
            scopes: [...environment.msal.scopes],
        },
        loginFailedRoute: LOGIN_URL,
    }
}
