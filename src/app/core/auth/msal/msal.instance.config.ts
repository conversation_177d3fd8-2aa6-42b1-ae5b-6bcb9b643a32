import { inject } from '@angular/core'

import { environment } from '@environments/environment'
import { LoggerService } from '@core/logging/logger.service'

import {
    BrowserCacheLocation,
    IPublicClientApplication,
    LogLevel,
    PublicClientApplication,
} from '@azure/msal-browser'

/**
 * Creates an MSAL instance.
 * @constructor
 */
export function MsalInstanceFactory(): IPublicClientApplication {
    const logger = inject(LoggerService)

    return new PublicClientApplication({
        auth: {
            clientId: environment.msal.clientId,
            authority: environment.msal.authority,
            redirectUri: environment.msal.redirectUri,
            postLogoutRedirectUri: environment.msal.redirectUri,
        },
        cache: {
            cacheLocation: BrowserCacheLocation.SessionStorage,
        },
        system: {
            allowPlatformBroker: false,
            loggerOptions: {
                logLevel: environment.production
                    ? LogLevel.Warning
                    : LogLevel.Verbose,
                piiLoggingEnabled: false, // no logging of sensitive user data
                loggerCallback: (logLevel, message) => {
                    logger.log(`[MSAL] [${LogLevel[logLevel]}] ${message}`)
                },
            },
        },
    })
}
