import { environment } from '@environments/environment'

import { MsalInterceptorConfiguration } from '@azure/msal-angular'
import { InteractionType } from '@azure/msal-browser'

/**
 * Maps backend functions api url to the resources protected. The interceptor then will attach tokens to HTTP requests.
 * @constructor
 */
export function MsalInterceptorConfigFactory(): MsalInterceptorConfiguration {
    const protectedResourceMap = new Map<string, string[]>()
    protectedResourceMap.set(
        environment.apiUrl, // attach tokens only for requests to the backend API
        environment.msal.apiScope
    )

    return {
        interactionType: InteractionType.Redirect,
        protectedResourceMap,
    }
}
