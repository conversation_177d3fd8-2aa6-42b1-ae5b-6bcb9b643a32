/* eslint-disable no-console */
import { Injectable } from '@angular/core'

import { environment } from '@environments/environment'

export enum LogLevel {
    OFF = 0,
    ERROR = 1,
    WARN = 2,
    INFO = 3,
    DEBUG = 4,
    LOG = 5,
}

@Injectable({ providedIn: 'root' })
export class LoggerService {
    private currentLogLevel: LogLevel = environment.production
        ? LogLevel.WARN
        : LogLevel.DEBUG

    log(message: any, ...optionalParams: any[]): void {
        if (this.currentLogLevel >= LogLevel.LOG) {
            console.log(`[LOG] ${message}`, ...optionalParams)
        }
    }

    info(message: any, ...optionalParams: any[]): void {
        if (this.currentLogLevel >= LogLevel.INFO) {
            console.info(`[INFO] ${message}`, ...optionalParams)
        }
    }

    warn(message: any, ...optionalParams: any[]): void {
        if (this.currentLogLevel >= LogLevel.WARN) {
            console.warn(`[WARN] ${message}`, ...optionalParams)
        }
    }

    error(message: any, ...optionalParams: any[]): void {
        if (this.currentLogLevel >= LogLevel.ERROR) {
            console.error(`[ERROR] ${message}`, ...optionalParams)
        }
    }

    debug(message: any, ...optionalParams: any[]): void {
        if (this.currentLogLevel >= LogLevel.DEBUG) {
            console.debug(`[DEBUG] ${message}`, ...optionalParams)
        }
    }

    setLogLevel(level: LogLevel): void {
        this.currentLogLevel = level
    }
}
