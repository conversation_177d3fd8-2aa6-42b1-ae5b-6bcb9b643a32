<app-page-layout>
    <div upper>
        <h2>Playground for components</h2>
        <div [formGroup]="exampleInputFieldForm">
            <app-input-field-text
                placeholder="1000"
                icon="search"
                formControlName="exampleInputField"
                hint="Example hint"
                label="Example number formatted input field"
            ></app-input-field-text>
        </div>

        <app-button (buttonClick)="setExampleInputField()"
            >Set Example input field</app-button
        >

        <div class="flex">
            <div [formGroup]="datepickerTestForm">
                <div class="content">
                    <app-datetimepicker-field
                        format="date"
                        formControlName="inputFieldDatepicker"
                        (valueChange)="onDatepickerChange($event)"
                    ></app-datetimepicker-field>
                </div>
                <p class="p-4 bg-white">
                    {{ inputFieldDatepicker }}
                </p>
            </div>
            <div [formGroup]="datetimepickerTestForm">
                <div class="content">
                    <app-datetimepicker-field
                        format="datetime"
                        formControlName="inputFieldDatetimepicker"
                        (valueChange)="onDatetimepickerChange($event)"
                    ></app-datetimepicker-field>
                </div>
                <p class="p-4 bg-white">
                    {{ inputFieldDatetimepicker }}
                </p>
            </div>
        </div>
    </div>
    <div lower>
        <app-button [routerLink]="LOGIN_URL" variant="secondary"
            >Login</app-button
        >
        <app-button variant="round" icon="close"></app-button>
        <app-button icon="save">Save</app-button>
        <div style="width: 100%" class="flex gap-4 flex-col">
            <div class="gap-4">
                <app-input-field-text
                    class="w-96"
                    placeholder="search"
                ></app-input-field-text>
                <app-button icon="add">Add</app-button>
            </div>
        </div>
        <app-button icon="add" (buttonClick)="go()">Set true</app-button>
        <app-button icon="add" (buttonClick)="stop()">Set false</app-button>
        {{ queryParamStore.get().variable1 }}
        {{ queryParamStore.get().variable2 }}
    </div>
</app-page-layout>
