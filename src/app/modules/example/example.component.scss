.upper-content {
    display: flex;
    height: auto;
    padding: 24px;
    align-items: flex-start;
    gap: 24px;
    flex-shrink: 0;
    align-self: stretch;
}

.lower-content {
    display: flex;
    min-height: 70vh;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    flex: 1 0 0;
    align-self: stretch;
    background: var(--color-secondary-Silver-Planet, #f0f5f5);
}

.content {
    margin: 2em;
    display: flex;
    gap: 2em;
}

app-example-table {
    width: 100%;
}
