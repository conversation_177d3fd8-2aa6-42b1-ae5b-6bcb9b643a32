import { Component } from '@angular/core'
import {
    FormControl,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'
import { RouterModule } from '@angular/router'

import { InputFieldTextComponent } from '@app/shared/components/input-field-text/input-field-text.component'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { LOGIN_URL } from '@core/auth/auth.constants'
import { ButtonComponent } from '@shared/components/button/button.component'
import { DatetimepickerFieldComponent } from '@shared/components/datetimepicker-field/datetimepicker-field.component'
import { PageLayoutComponent } from '@shared/components/layout/page-layout/page-layout.component'

@Component({
    selector: 'app-example',
    imports: [
        ButtonComponent,
        InputFieldTextComponent,
        RouterModule,
        DatetimepickerFieldComponent,
        ReactiveFormsModule,
        PageLayoutComponent,
    ],
    templateUrl: './example.component.html',
    standalone: true,
    styleUrl: './example.component.scss',
})
export class ExampleComponent {
    protected readonly LOGIN_URL = LOGIN_URL
    queryParamStore = createURLVariables('variable1', 'variable2')

    inputFieldDatetimepicker: Date | null = null
    inputFieldDatepicker: Date | null = null

    exampleInputFieldForm = new FormGroup({
        exampleInputField: new FormControl('', [Validators.required]),
    })

    datetimepickerTestForm = new FormGroup({
        inputFieldDatetimepicker: new FormControl<Date | null>(
            null,
            Validators.required
        ),
    })

    datepickerTestForm = new FormGroup({
        inputFieldDatepicker: new FormControl<Date | null>(
            null,
            Validators.required
        ),
    })

    onDatetimepickerChange(value: Date | null) {
        this.inputFieldDatetimepicker = value
    }

    onDatepickerChange(value: Date | null) {
        this.inputFieldDatepicker = value
    }

    setExampleInputField() {
        this.exampleInputFieldForm.controls['exampleInputField'].setValue(
            '12345'
        )
    }

    go() {
        this.queryParamStore.update((vars) => {
            vars.variable1 = 'true'
            vars.variable2 = 'true'
        })
    }

    stop() {
        this.queryParamStore.update((vars) => {
            vars.variable1 = 'false'
            vars.variable2 = 'false'
        })
    }
}
