import { Component } from '@angular/core'
import { RouterModule } from '@angular/router'

import { AuthService } from '@app/core/auth/auth.service'
import { ButtonComponent } from '@shared/components/button/button.component'

import { TranslatePipe } from '@ngx-translate/core'

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [ButtonComponent, RouterModule, TranslatePipe],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
})
export class LoginComponent {
    constructor(private authService: AuthService) {}

    onSignIn(): void {
        this.authService.signIn()
    }
}
