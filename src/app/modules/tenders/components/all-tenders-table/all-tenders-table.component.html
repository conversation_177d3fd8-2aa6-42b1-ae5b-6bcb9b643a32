<div class="content">
    <table
        mat-table
        [dataSource]="dataSource"
        matSort
        (matSortChange)="onSortChange($event)"
        multiTemplateDataRows
    >
        <!-- EXPAND TOGGLE ICON COLUMN -->
        <ng-container matColumnDef="expand">
            <th mat-header-cell *matHeaderCellDef aria-label="row actions">
                &nbsp;
            </th>
            <td mat-cell *matCellDef="let element">
                <button
                    type="button"
                    mat-icon-button
                    aria-label="expand row"
                    (click)="toggle(element); $event.stopPropagation()"
                    class="example-toggle-button"
                    [class.example-toggle-button-expanded]="isExpanded(element)"
                >
                    <mat-icon>keyboard_arrow_down</mat-icon>
                </button>
            </td>
        </ng-container>

        <!-- OTHER COLUMNS -->
        <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef>
                {{ 'tender.table.title' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">{{ element.title }}</td>
        </ng-container>

        <ng-container matColumnDef="client">
            <th mat-header-cell *matHeaderCellDef>
                {{ 'tender.details.client.label' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.client ?? '' }}
            </td>
        </ng-container>

        <ng-container matColumnDef="creationTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.table.created-at' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.creationTime | localeDate: 'short' }}
            </td>
        </ng-container>

        <ng-container matColumnDef="lastUpdatedTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.table.last-modified' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.lastUpdatedTime | localeDate: 'short' }}
            </td>
        </ng-container>

        <ng-container matColumnDef="contractValue">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.table.contract-value' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.contractValue | localeCurrency }}
            </td>
        </ng-container>

        <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
                {{ 'tender.table.actions' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                <div class="actions-container">
                    <app-button
                        (buttonClick)="onDeleteClick($event, element)"
                        variant="plain"
                        class="inline-flex"
                    >
                        <span class="material-symbols-outlined"> delete </span>
                    </app-button>
                    <span class="action-separator"></span>

                    <app-button
                        (buttonClick)="onChatClick($event, element)"
                        variant="plain"
                        class="inline-flex"
                        [disabled]="true"
                    >
                        <span class="material-symbols-outlined"> chat </span>
                    </app-button>
                </div>
            </td>
        </ng-container>

        <!-- EXPANDED DETAIL ROW DEFINITION -->
        <ng-container matColumnDef="expandedDetail">
            <td
                mat-cell
                *matCellDef="let element"
                [attr.colspan]="columnsToDisplayWithExpand.length"
            >
                <div
                    class="example-element-detail-wrapper"
                    [class.example-element-detail-wrapper-expanded]="
                        isExpanded(element)
                    "
                >
                    <!-- 2-column grid -->
                    <div class="example-element-detail">
                        <!-- LEFT -->
                        <div class="comment-field">
                            <mat-label>{{
                                'tender.details.comment.label' | translate
                            }}</mat-label>
                            <textarea
                                cdkTextareaAutosize
                                cdkAutosizeMinRows="10"
                                cdkAutosizeMaxRows="100"
                                style="width: 100%; resize: none"
                                disabled
                                >{{ element.comment }}</textarea
                            >
                        </div>

                        <!-- RIGHT -->
                        <div class="tender-metadata-field">
                            <div class="status">{{ element.status }}</div>
                            <div class="rating">
                                <ng-container>
                                    @for (
                                        icon of element.rating | starIcons: 5;
                                        track $index
                                    ) {
                                        <mat-icon>{{ icon }}</mat-icon>
                                    }
                                </ng-container>
                            </div>
                            <div class="favorite">
                                <mat-icon>
                                    {{
                                        element.isFavorite
                                            ? 'favorite'
                                            : 'favorite_border'
                                    }}
                                </mat-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
        </ng-container>

        <!-- 5. HEADER ROW -->
        <tr mat-header-row *matHeaderRowDef="columnsToDisplayWithExpand"></tr>

        <!-- 6. DATA ROW -->
        <tr
            mat-row
            *matRowDef="let row; columns: columnsToDisplayWithExpand"
            class="example-element-row"
            [class.example-expanded-row]="isExpanded(row)"
            (click)="onModalEditOpen(row.id)"
            (keydown.enter)="onModalEditOpen(row.id)"
            tabindex="0"
        ></tr>

        <!-- 7. DETAIL ROW -->
        <tr
            mat-row
            *matRowDef="let row; columns: ['expandedDetail']"
            class="example-detail-row"
        ></tr>

        <!-- NO DATA ROW -->
        <ng-container matColumnDef="noDataRow">
            <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell" colspan="4">
                    <span class="p-4">
                        {{ 'table.no-data.message' | translate }}</span
                    >
                </td>
            </tr>
        </ng-container>
    </table>

    <!-- LOADING SPINNER / PAGINATOR -->
    @if ((loading$ | async) === true) {
        <div class="spinner-container">
            <mat-spinner color="primary" [diameter]="50"></mat-spinner>
        </div>
    } @else {
        <mat-paginator
            [pageSizeOptions]="[5, 10, 20]"
            showFirstLastButtons
            [pageSize]="pageSize()"
            [pageIndex]="pageNumber()"
            [length]="totalElements()"
            (page)="onPageChange($event)"
            aria-label="Select page of periodic elements"
        >
        </mat-paginator>
    }
</div>

<!-- URL QUERY PARAMS -->
@if (queryParamStore.get().modal === 'edit') {
    <app-tender-modal-details
        (closed)="onModalClosed()"
    ></app-tender-modal-details>
}
