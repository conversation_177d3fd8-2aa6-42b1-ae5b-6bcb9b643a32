/*──────────────────────────────────────────────────────────────────────────
  TABLE & PAGINATOR BACKGROUND
──────────────────────────────────────────────────────────────────────────*/
.mat-mdc-table,
.mat-mdc-paginator {
    background-color: var(--white) !important;
}

/*──────────────────────────────────────────────────────────────────────────
  BORDER COLLAPSE
──────────────────────────────────────────────────────────────────────────*/
table[mat-table],
table.mat-mdc-table,
.mdc-data-table__table {
    border-collapse: collapse !important;
    border-spacing: 0 !important;
}

/*──────────────────────────────────────────────────────────────────────────
  ROW / CELL BORDER & CURSOR
──────────────────────────────────────────────────────────────────────────*/
.mat-mdc-row .mat-mdc-cell {
    border-top: none !important;
    border-bottom: 1px solid black;
    cursor: pointer;
}

/*──────────────────────────────────────────────────────────────────────────
  HOVER STATE
──────────────────────────────────────────────────────────────────────────*/
.mat-mdc-row:hover .mat-mdc-cell {
    background-color: var(--color-secondary-Silver-Planet);
}

/*──────────────────────────────────────────────────────────────────────────
  EXPANDED MASTER ROW
──────────────────────────────────────────────────────────────────────────*/
tr.example-element-row.example-expanded-row .mat-mdc-cell {
    background-color: var(--color-secondary-Silver-Planet) !important;
    border-bottom: none !important;
}

/*──────────────────────────────────────────────────────────────────────────
  DETAIL ROW CELLS
──────────────────────────────────────────────────────────────────────────*/
tr.example-detail-row {
    height: 0; /* collapsed height */
}

tr.example-detail-row td,
tr.example-detail-row .mat-mdc-cell,
tr.example-detail-row .cdk-cell {
    border-top: none !important;
    border-bottom: none !important;
    cursor: default;
    padding: 0;
}

/*──────────────────────────────────────────────────────────────────────────
  DETAIL PANEL WRAPPER
──────────────────────────────────────────────────────────────────────────*/
.example-element-detail-wrapper {
    overflow: hidden;
    display: grid;
    grid-template-rows: 0fr;
    grid-template-columns: 100%;
    transition: grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0 1rem 0 1rem;
}

.example-element-detail-wrapper-expanded {
    grid-template-rows: 1fr;
    background-color: var(--color-secondary-Silver-Planet);
}

/*──────────────────────────────────────────────────────────────────────────
  DETAIL CONTENT LAYOUT
──────────────────────────────────────────────────────────────────────────*/
.example-element-detail {
    display: grid;
    grid-template-columns: 2fr 1fr;
    column-gap: 16px;
    align-items: center;
    min-height: 0;
}

/*──────────────────────────────────────────────────────────
  LEFT COLUMN
──────────────────────────────────────────────────────────*/
.example-element-detail .comment-field {
    display: flex;
    flex-direction: column;
    gap: 4px;

    mat-label {
        font-weight: 600;
    }

    textarea {
        width: 100%;
        max-height: 80px;
        padding: 8px;
        resize: vertical;
        font-family: inherit;
        font-size: 0.9rem;
        background-color: transparent;
        border: 1px solid var(--disabled);
        border-radius: 4px;
    }
}

/*──────────────────────────────────────────────────────────
  RIGHT COLUMN
──────────────────────────────────────────────────────────*/
.example-element-detail .tender-metadata-field {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .status {
        font-weight: 600;
        font-size: 0.95rem;
    }

    .rating {
        display: flex;
        gap: 4px;

        mat-icon {
            font-size: 20px;
            vertical-align: middle;
        }
    }
}

/*──────────────────────────────────────────────────────────────────────────
  TOGGLE BUTTON ROTATION
──────────────────────────────────────────────────────────────────────────*/
.example-toggle-button {
    transition: transform 225ms cubic-bezier(0.4, 0, 0.2, 1);
}

.example-toggle-button-expanded {
    transform: rotate(180deg);
}

/*──────────────────────────────────────────────────────────────────────────
 COLUMN WIDTHS & CONTENT CONTAINERS
──────────────────────────────────────────────────────────────────────────*/
.content {
    background-color: var(--white);
    box-shadow:
        0 1px 1px 0 rgba(0, 0, 0, 0.2),
        0 2px 2px 0 rgba(0, 0, 0, 0.14),
        0 1px 5px 0 rgba(0, 0, 0, 0.12);
    border-radius: 0.25rem;
}

.mat-column-title {
    max-width: 180px !important;
    min-width: 180px !important;
}

.mat-column-creationTime,
.mat-column-lastUpdatedTime {
    max-width: 100px !important;
    min-width: 100px !important;
}

.mat-column-createdBy {
    max-width: 150px !important;
    min-width: 150px !important;
}

.mat-column-contractValue {
    max-width: 120px !important;
    min-width: 120px !important;
}

.mat-column-actions {
    max-width: 100px !important;
    min-width: 100px !important;
}

/*──────────────────────────────────────────────────────────────────────────
 SPINNER & PAGINATOR CONTAINERS
──────────────────────────────────────────────────────────────────────────*/
.spinner-container {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/*──────────────────────────────────────────────────────────────────────────
 ACTION BUTTONS
──────────────────────────────────────────────────────────────────────────*/
.action-separator {
    display: inline-block;
    width: 1px;
    height: 24px;
    background-color: #ccc;
    margin: 0 0.5rem;
    color: var(--primary-color);
}

.actions-container {
    display: flex;

    ::ng-deep button {
        margin: 0;
    }
}
