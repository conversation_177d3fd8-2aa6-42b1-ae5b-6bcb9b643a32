import { CdkTextareaAutosize } from '@angular/cdk/text-field'
import { CommonModule } from '@angular/common'
import {
    Component,
    inject,
    OnDestroy,
    OnInit,
    signal,
    viewChild,
} from '@angular/core'
import { MatIconButton } from '@angular/material/button'
import { MatDialog } from '@angular/material/dialog'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatIcon } from '@angular/material/icon'
import {
    MatPaginator,
    MatPaginatorModule,
    PageEvent,
} from '@angular/material/paginator'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatSort, MatSortModule, Sort } from '@angular/material/sort'
import {
    MatNoDataRow,
    MatTableDataSource,
    MatTableModule,
} from '@angular/material/table'
import { Router, RouterModule } from '@angular/router'

import {
    CollectionSortDirectionDTO,
    TenderDTO,
    TenderPagedDTO,
    TenderSortableFieldsDTO,
    TenderStatusDTO,
} from '@app/api'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { LoggerService } from '@core/logging/logger.service'
import { TenderModalDetailsComponent } from '@modules/tenders/components/tender-modal-details/tender-modal-details.component'
import { AllTendersModalTypes } from '@modules/tenders/pages/all-tenders/all-tenders.component'
import { TenderService } from '@modules/tenders/services/tender.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '@shared/components/confirm-dialog/confirm-dialog.component'
import {
    DEFAULT_TABLE_PAGE_NUMBER,
    DEFAULT_TABLE_SIZE,
} from '@shared/constants/table.constants'
import { PopUpService } from '@shared/services/pop-up.service'
import { LocaleCurrencyPipe } from '@shared/utils/custom-pipes/locale-currency.pipe'
import { LocaleDatePipe } from '@shared/utils/custom-pipes/locale-date.pipe'
import { StarIconsPipe } from '@shared/utils/custom-pipes/star-icons.pipe'

import { TranslateModule, TranslateService } from '@ngx-translate/core'
import {
    BehaviorSubject,
    finalize,
    firstValueFrom,
    interval,
    Subscription,
    switchMap,
} from 'rxjs'

@Component({
    selector: 'app-all-tenders-table',
    imports: [
        MatTableModule,
        CommonModule,
        MatFormFieldModule,
        MatSortModule,
        MatPaginatorModule,
        RouterModule,
        TranslateModule,
        MatProgressSpinnerModule,
        MatNoDataRow,
        TenderModalDetailsComponent,
        ButtonComponent,
        LocaleCurrencyPipe,
        LocaleDatePipe,
        MatIcon,
        MatIconButton,
        StarIconsPipe,
        CdkTextareaAutosize,
    ],
    templateUrl: './all-tenders-table.component.html',
    styleUrl: './all-tenders-table.component.scss',
    standalone: true,
})
export class AllTendersTableComponent implements OnInit, OnDestroy {
    private readonly translate = inject(TranslateService)
    private pollingSubscription: Subscription | null = null

    sort = viewChild<MatSort>(MatSort)
    paginator = viewChild<MatPaginator>(MatPaginator)

    loading$ = new BehaviorSubject<boolean>(true)

    dataSource!: MatTableDataSource<TenderDTO>
    pagedTender: TenderPagedDTO | undefined = undefined
    displayedColumns: string[] = [
        'title',
        'client',
        TenderSortableFieldsDTO.creationTime,
        TenderSortableFieldsDTO.lastUpdatedTime,
        TenderSortableFieldsDTO.contractValue,
        'actions',
    ]

    columnsToDisplayWithExpand = ['expand', ...this.displayedColumns]
    private expandedId: string | null = null
    expandedElement: TenderDTO | null | undefined

    isExpanded(element: TenderDTO) {
        return element.id === this.expandedId
    }

    toggle(element: TenderDTO) {
        this.expandedId = this.expandedId === element.id ? null : element.id

        // after refresh re-open originally expanded element
        this.expandedElement = this.expandedId
            ? (this.dataSource.data.find((row) => row.id === this.expandedId) ??
              null)
            : null
    }

    queryParamStore = createURLVariables('modal', 'selectedTender')
    readonly dialog = inject(MatDialog)

    pageNumber = signal(DEFAULT_TABLE_PAGE_NUMBER)
    pageSize = signal(DEFAULT_TABLE_SIZE)
    sortBy = signal<TenderSortableFieldsDTO>(
        TenderSortableFieldsDTO.creationTime
    )
    sortDirection = signal<CollectionSortDirectionDTO>(
        CollectionSortDirectionDTO.desc
    )
    totalElements = signal(0)

    constructor(
        private tenderService: TenderService,
        private popUpService: PopUpService,
        private logger: LoggerService,
        private router: Router
    ) {}

    ngOnInit(): void {
        this.dataSource = new MatTableDataSource(this.pagedTender?.data)
        this.fetchTenders()
    }

    onSortChange(sort: Sort): void {
        this.sortBy.set(sort.active as TenderSortableFieldsDTO)
        this.sortDirection.set(sort.direction as CollectionSortDirectionDTO)

        if (!this.sortDirection()) this.sortDirection.set('desc')

        this.fetchTenders()
    }

    onPageChange(event: PageEvent): void {
        this.pageSize.set(event.pageSize)
        this.pageNumber.set(event.pageIndex)
        this.fetchTenders()
    }

    public changeFilter(filter: string) {
        if (!this.dataSource) {
            this.logger.warn('changed dataSource.filter too early')
            return
        }

        this.dataSource.filter = filter
    }

    fetchTenders(reset = false) {
        this.loading$.next(true)

        if (reset) {
            this.pageNumber.set(DEFAULT_TABLE_PAGE_NUMBER)
            this.pageSize.set(DEFAULT_TABLE_SIZE)
        }

        this.tenderService
            .getAllTenders(
                this.pageNumber(),
                this.pageSize(),
                this.sortBy(),
                this.sortDirection()
            )
            .pipe(finalize(() => this.loading$.next(false)))
            .subscribe({
                next: (pagedResult) => {
                    this.dataSource.data = pagedResult.data
                    this.pageNumber.set(pagedResult.paging.pageNumber)
                    this.pageSize.set(pagedResult.paging.pageSize)
                    this.totalElements.set(pagedResult.paging.totalElements)
                    this.checkForOngoingAnalysis()
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.fetch-tenders'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
    }

    /**
     * If any row in the current dataSource is still in status === 'ANALYZING',
     * start or continue a polling loop. As soon as no row is ANALYZING,
     * we unsubscribe and stop polling.
     */
    private checkForOngoingAnalysis() {
        const rows = this.dataSource.data || []
        const anyAnalyzing = rows.some(
            (row: TenderDTO) =>
                row.status === TenderStatusDTO.Analyzing.toUpperCase()
        )

        if (anyAnalyzing) {
            if (!this.pollingSubscription) {
                this.pollingSubscription = interval(5000) // every 5 seconds
                    .pipe(
                        switchMap(() =>
                            this.tenderService.getAllTenders(
                                this.pageNumber(),
                                this.pageSize(),
                                this.sortBy(),
                                this.sortDirection()
                            )
                        )
                    )
                    .subscribe({
                        next: (paged: TenderPagedDTO) => {
                            this.dataSource.data = paged.data
                            this.pageNumber.set(paged.paging.pageNumber)
                            this.pageSize.set(paged.paging.pageSize)
                            this.totalElements.set(paged.paging.totalElements)

                            const stillAnalyzing = this.dataSource.data.some(
                                (r: TenderDTO) =>
                                    r.status ===
                                    TenderStatusDTO.Analyzing.toUpperCase()
                            )
                            if (!stillAnalyzing) {
                                this.stopPollingIfAny()
                            }
                        },
                        error: () => {
                            this.stopPollingIfAny()
                        },
                    })
            }
        } else {
            this.stopPollingIfAny()
        }
    }

    private stopPollingIfAny() {
        if (this.pollingSubscription) {
            this.pollingSubscription.unsubscribe()
            this.pollingSubscription = null
        }
    }

    public async deleteItem(tender: TenderDTO) {
        const header = this.translate.instant(
            'confirm-dialog.delete-tender.header'
        )
        const text = this.translate.instant('confirm-dialog.delete-tender.text')

        const dialogData: ConfirmDialogData = {
            header: header,
            text: text,
        }

        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            data: dialogData,
        })

        const shouldContinue: boolean = await firstValueFrom(
            dialogRef.afterClosed()
        )
        if (!shouldContinue) return

        this.tenderService.deleteTender(tender.id).subscribe({
            next: () => {
                this.fetchTenders()
                const successMessage = this.translate.instant(
                    'popup.success.delete-tender'
                )
                this.popUpService.openSuccessPopUp(successMessage)
            },
            error: () => {
                const errorMessage = this.translate.instant(
                    'popup.error.delete-tender'
                )
                this.popUpService.openErrorPopUp(errorMessage)
            },
        })
    }

    onDeleteClick(event: MouseEvent, element: TenderDTO) {
        event.stopPropagation()
        void this.deleteItem(element)
    }

    onChatClick(event: MouseEvent, element: TenderDTO) {
        event.stopPropagation()
        void this.router.navigate(['/tender', element.id, 'chat'])
    }

    onModalEditOpen(tenderId: string) {
        this.updateModal('edit', tenderId)
    }

    private updateModal(modal: AllTendersModalTypes, tenderId?: string) {
        this.queryParamStore.update((vars) => {
            vars.modal = modal
            vars.selectedTender = tenderId
            return vars
        })
    }

    onModalClosed() {
        this.updateModal(undefined, undefined)
        this.fetchTenders()
    }

    ngOnDestroy(): void {
        if (this.pollingSubscription) {
            this.pollingSubscription.unsubscribe()
            this.pollingSubscription = null
        }
    }
}
