<div class="tender-analysis-container">
    <div class="w-full p-6">
        <div class="flex items-center justify-between mb-6 relative">
            <div class="flex items-center space-x-2">
                <h2 class="flex items-center gap-2 space-x-3">
                    {{ 'tender.analysis.result.label' | translate }}
                    <mat-icon
                        matTooltip="{{
                            'tender.analysis.analyze-tender.hint' | translate
                        }}"
                        matTooltipPosition="above"
                        aria-label="Information"
                        fontIcon="help_outline"
                        class="material-icons-outlined mb-1 cursor-pointer text-primary"
                        style="font-size: 1.5rem"
                    ></mat-icon>
                </h2>
                @if (!isAnalysisInProgress()) {
                    <div>
                        <app-button
                            (buttonClick)="onCopyReportFormatted()"
                            [matTooltip]="
                                'button.default.hint.copy-report' | translate
                            "
                            [matTooltipPosition]="'above'"
                            class="no-mt"
                            variant="secondary"
                            size="small"
                        >
                            <span class="material-symbols-outlined">
                                {{
                                    copyReportSuccess()
                                        ? 'check'
                                        : 'content_copy'
                                }}
                            </span>
                        </app-button>
                    </div>
                }
                @if (isAnalysisInProgress()) {
                    <h4 class="text-red-600">
                        {{ 'modal.analyze.analysis-in-progress' | translate }}
                    </h4>
                }
            </div>
            <div>
                <app-button
                    icon="autorenew"
                    (buttonClick)="analyzeTender()"
                    [disabled]="isAnalysisInProgress()"
                >
                    {{ 'modal.analyze-button' | translate }}
                </app-button>
            </div>
            @if (loading$.getValue()) {
                <div class="loading-overlay">
                    <mat-progress-spinner
                        mode="indeterminate"
                        diameter="50"
                    ></mat-progress-spinner>
                </div>
            }
        </div>
        <app-separator separatorType="horizontal"></app-separator>
        <div class="w-full p-6">
            <span class="analysis-label"></span>
            <div #report class="analysis-value">
                @if (analysisResult()?.analysisResult) {
                    <markdown [data]="markdown().toString()"></markdown>
                }
            </div>
            <div class="pt-4">
                <app-separator separatorType="horizontal"></app-separator>
                <div class="pt-4 flex flex-row gap-8">
                    <span class="analysis-label"
                        >{{
                            'tender.analysis.prompt-tokens.label' | translate
                        }}:</span
                    >
                    <div class="analysis-value">
                        <span>{{ analysisResult()?.promptTokens }}</span>
                    </div>
                    <span class="analysis-label"
                        >{{
                            'tender.analysis.completion-tokens.label'
                                | translate
                        }}:</span
                    >
                    <div class="analysis-value">
                        <span>{{ analysisResult()?.completionTokens }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
