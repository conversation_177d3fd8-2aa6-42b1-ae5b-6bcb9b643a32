import { Clipboard } from '@angular/cdk/clipboard'
import {
    Component,
    computed,
    ElementRef,
    inject,
    input,
    model,
    OnDestroy,
    OnInit,
    signal,
    ViewChild,
} from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { MatIcon } from '@angular/material/icon'
import { MatProgressSpinner } from '@angular/material/progress-spinner'
import { MatTooltip } from '@angular/material/tooltip'

import { AnalysisResultDTO, TenderDTO, TenderStatusDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'
import { AnalysisService } from '@modules/tenders/services/analysis.service'
import { TenderService } from '@modules/tenders/services/tender.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '@shared/components/confirm-dialog/confirm-dialog.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { PopUpService } from '@shared/services/pop-up.service'
import { writeHtmlAndText } from '@shared/utils/general-utils'

import { TranslatePipe, TranslateService } from '@ngx-translate/core'
import { MarkdownComponent, MarkdownService } from 'ngx-markdown'
import {
    BehaviorSubject,
    finalize,
    firstValueFrom,
    interval,
    Subject,
    Subscription,
    switchMap,
    takeUntil,
    takeWhile,
} from 'rxjs'

@Component({
    selector: 'app-tender-analysis',
    imports: [
        SeparatorComponent,
        TranslatePipe,
        ButtonComponent,
        MatProgressSpinner,
        ReactiveFormsModule,
        MatIcon,
        MatTooltip,
        MarkdownComponent,
    ],
    templateUrl: './tender-analysis.component.html',
    standalone: true,
    styleUrl: './tender-analysis.component.scss',
})
export class TenderAnalysisComponent implements OnInit, OnDestroy {
    private readonly translate = inject(TranslateService)
    readonly dialog = inject(MatDialog)
    private destroy$ = new Subject<void>()

    tenderId = input<string>('')

    analysisResult = model<AnalysisResultDTO | null>(null)
    tender = model<TenderDTO | null>(null)
    tenderCurrentStatus = model<TenderStatusDTO | null>(null)
    isAnalysisInProgress = computed(
        () =>
            this.tenderCurrentStatus() ===
            TenderStatusDTO.Analyzing.toUpperCase()
    )

    @ViewChild('report', { static: true })
    reportRef!: ElementRef<HTMLTextAreaElement>
    copyReportSuccess = signal(false)

    loading$ = new BehaviorSubject<boolean>(false)
    private statusPollSub?: Subscription

    public markdown = computed(() => {
        const raw = this.analysisResult()?.analysisResult ?? ''
        return this.mdService.parse(raw)
    })

    constructor(
        private analysisService: AnalysisService,
        private tenderService: TenderService,
        private logger: LoggerService,
        private popUpService: PopUpService,
        private mdService: MarkdownService,
        private clipboard: Clipboard
    ) {}

    ngOnInit() {
        this.loadTender().then(() => {
            if (this.isAnalysisInProgress()) {
                this.startStatusPolling()
            }
        })
    }

    private async loadTender() {
        try {
            const dto = await firstValueFrom(
                this.tenderService.getTenderById(this.tenderId())
            )
            this.updateTenderInformation(dto)
        } catch (err) {
            this.logger.warn('Failed loading tender', err)
        }
    }

    public async analyzeTender(): Promise<void> {
        if (!this.tenderId()) {
            this.logger.warn('analyzeTender: tenderId was undefined')
            return
        }

        const analysis = this.tender()?.analysisResult
        if (
            analysis &&
            analysis.analysisResult &&
            analysis.analysisResult.length > 0
        ) {
            const header = this.translate.instant(
                'confirm-dialog.analyze-tender.header'
            )
            const text = this.translate.instant(
                'confirm-dialog.analyze-tender.text'
            )

            const dialogData: ConfirmDialogData = {
                header: header,
                text: text,
            }

            const dialogRef = this.dialog.open(ConfirmDialogComponent, {
                data: dialogData,
            })

            const shouldContinue: boolean = await firstValueFrom(
                dialogRef.afterClosed()
            )
            if (!shouldContinue) return
        }

        this.loading$.next(true)
        this.analysisResult.set(null)
        this.tenderCurrentStatus.set(null)

        this.analysisService
            .analyzeTender(this.tenderId())
            .pipe(
                takeUntil(this.destroy$),
                finalize(() => this.loading$.next(false))
            )
            .subscribe({
                next: (result: AnalysisResultDTO) => {
                    if (result) {
                        this.analysisResult.set(result)
                        this.loadTender().then(() => {
                            if (this.isAnalysisInProgress()) {
                                this.startStatusPolling()
                            }
                        })
                    }
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.analyze-tender'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
    }

    private startStatusPolling() {
        this.statusPollSub?.unsubscribe()
        this.statusPollSub = interval(10_000)
            .pipe(
                switchMap(() =>
                    this.tenderService.getTenderById(this.tenderId())
                ),
                takeUntil(this.destroy$),
                takeWhile(
                    (dto) =>
                        dto.status === TenderStatusDTO.Analyzing.toUpperCase(),
                    true
                )
            )
            .subscribe((dto) => {
                this.updateTenderInformation(dto)
            })
    }

    updateTenderInformation(newTender: TenderDTO) {
        this.tender.set(newTender)
        if (newTender.status) {
            this.tenderCurrentStatus.set(newTender.status)
        }
        if (
            newTender.status !== TenderStatusDTO.Analyzing.toUpperCase() &&
            newTender.analysisResult
        ) {
            this.analysisResult.set(newTender.analysisResult)
        }
    }

    async onCopyReportFormatted() {
        const el = this.reportRef.nativeElement
        const html = el.innerHTML
        const text = el.innerText

        let copied = false
        if ('clipboard' in navigator && typeof ClipboardItem !== 'undefined') {
            copied = await writeHtmlAndText(html, text)
        }

        if (!copied) {
            // fallback: plain-text
            this.clipboard.copy(text)
        }

        this.copyReportSuccess.set(true)
        setTimeout(() => this.copyReportSuccess.set(false), 2000)
    }

    ngOnDestroy() {
        this.statusPollSub?.unsubscribe()
        this.destroy$.next()
        this.destroy$.complete()
    }

    protected readonly TenderStatusDTO = TenderStatusDTO
}
