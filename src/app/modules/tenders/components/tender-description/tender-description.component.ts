import { Component, effect, inject, input, output, signal } from '@angular/core'
import {
    FormControl,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'

import { TenderDTO, TenderUpdateDTO } from '@app/api'
import { LoggerService } from '@app/core/logging/logger.service'
import { ButtonComponent } from '@app/shared/components/button/button.component'
import { InputTextareaComponent } from '@app/shared/components/input-textarea/input-textarea.component'
import { PopUpService } from '@shared/services/pop-up.service'

import { TenderService } from '../../services/tender.service'

import { TranslateModule, TranslateService } from '@ngx-translate/core'

@Component({
    selector: 'app-tender-description',
    imports: [
        InputTextareaComponent,
        TranslateModule,
        ReactiveFormsModule,
        ButtonComponent,
    ],
    templateUrl: './tender-description.component.html',
    styleUrl: './tender-description.component.scss',
    standalone: true,
})
export class TenderDescriptionComponent {
    private readonly translate = inject(TranslateService)

    public tenderId = input<string | null>('')
    public updatedTender = output()
    tender: TenderDTO | undefined

    protected form = new FormGroup({
        description: new FormControl('', [Validators.required]),
    })

    protected formValid = signal<boolean>(false)
    public isDirty = signal<boolean>(false)

    constructor(
        private tenderService: TenderService,
        private logger: LoggerService,
        private popUpService: PopUpService
    ) {
        this.form.valueChanges.subscribe(() => {
            this.isDirty.set(this.form.dirty)
            this.formValid.set(this.form.valid)
        })

        effect(() => {
            if (!this.tenderId) {
                this.logger.warn('No tenderId provided.')
                return
            }
            this.fetchTender()
        })
    }

    onCancel() {
        this.resetForm(this.tender)
        this.form.markAsPristine()
    }

    onSave() {
        if (!this.tenderId && !this.tender) {
            this.logger.warn('tenderId was undefined')
            return
        }

        if (this.form.valid) {
            const modifiedTender: TenderUpdateDTO =
                this.mapFormModelToTenderCreateDTO(
                    this.form.value.description ?? '',
                    this.tender!
                )

            this.tenderService
                .updateTender(this.tenderId()!, modifiedTender)
                .subscribe({
                    next: (result) => {
                        this.updatedTender.emit()
                        this.resetForm(result)

                        const successMessage = this.translate.instant(
                            'popup.success.update-tender-description'
                        )
                        this.popUpService.openSuccessPopUp(successMessage)
                    },
                    error: () => {
                        const errorMessage = this.translate.instant(
                            'popup.error.update-tender-description'
                        )
                        this.popUpService.openErrorPopUp(errorMessage)
                    },
                })
        }
    }

    private fetchTender(): void {
        if (this.tenderId()) {
            this.tenderService.getTenderById(this.tenderId()!).subscribe({
                next: (tender) => {
                    this.tender = tender
                    this.resetForm(tender)
                },
                error: () => {
                    const errorMessage = this.translate.instant(
                        'popup.error.fetch-tender-description'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
        }
    }

    resetForm(tender: TenderDTO | null | undefined) {
        if (tender) {
            const formModel = {
                description: tender.description,
            }

            this.form.reset(formModel)
            this.form.markAsPristine()
        }
    }

    mapFormModelToTenderCreateDTO(
        formFieldDescription: string,
        tender: TenderDTO
    ): TenderUpdateDTO {
        return {
            title: tender.title,
            sourceUrl: tender.sourceUrl,
            client: tender.client,
            submissionDate: tender.submissionDate,
            bindingDeadline: tender.bindingDeadline,
            contractDuration: tender.contractDuration,
            publicationDate: tender.publicationDate,
            questionDeadline: tender.questionDeadline,
            contractValue: tender.contractValue ?? undefined,
            maximumBudget: tender.maximumBudget ?? undefined,
            winningCriteria: tender.winningCriteria,
            weightingPriceQuality: tender.weightingPriceQuality,
            deliveryLocation: tender.deliveryLocation,
            description: formFieldDescription,
            workflowStatus: tender.workflowStatus ?? 'New',
        }
    }
}
