<div class="tender-details-container">
    <div class="user-info-container ap-verticalPad pt-2 pb-2 mb-4">
        <div class="inner-container">
            <div>
                <h4>
                    <span>
                        {{ 'tender.details.created-at.label' | translate }}:
                    </span>
                </h4>
            </div>
            <div>
                <h4>
                    <span class="ml-4"
                        >{{ tender()?.creationTime | localeDate: 'short' }}
                    </span>
                </h4>
            </div>
            <div>
                @if (tender()?.createdBy?.fullName) {
                    <h4>
                        <span>({{ tender()?.createdBy?.fullName }})</span>
                    </h4>
                }
            </div>
            <div>
                <h4>
                    <span>
                        {{ 'tender.details.modified-at.label' | translate }}:
                    </span>
                </h4>
            </div>
            <div>
                <h4>
                    <span class="ml-4"
                        >{{ tender()?.lastUpdatedTime | localeDate: 'short' }}
                    </span>
                </h4>
            </div>
            <div>
                @if (tender()?.lastUpdatedBy?.fullName) {
                    <h4>
                        <span>({{ tender()?.lastUpdatedBy?.fullName }})</span>
                    </h4>
                }
            </div>
        </div>
    </div>
    <app-separator separatorType="horizontal"></app-separator>
    <div class="w-full p-6 details-container">
        <div class="content-grid">
            <div class="large-field">
                <span class="details-label">
                    {{ 'tender.details.name-input.label' | translate }}:
                </span>
                <div class="details-value">
                    <span>{{ tender()?.title }}</span>
                </div>
            </div>
            <div class="large-field with-grid">
                <span class="details-label">
                    {{ 'tender.details.sourceUrl.label' | translate }}:
                </span>
                <div class="details-value">
                    <a [href]="tender()?.sourceUrl" target="_blank">
                        {{ tender()?.sourceUrl }}
                    </a>
                </div>
            </div>
            <span class="details-label">{{
                'tender.details.client.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.client }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.publicationDate.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{
                    tender()?.publicationDate | localeDate: 'mediumDate'
                }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.submissionDate.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{
                    tender()?.submissionDate
                        | localeDate: "MMM dd, yyyy '-' hh:mm a"
                }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.questionDeadline.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{
                    tender()?.questionDeadline
                        | localeDate: "MMM dd, yyyy '-' hh:mm a"
                }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.contractDuration.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.contractDuration }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.contractValue.label' | translate
            }}</span>
            <div class="details-value">
                @if (tender() && tender()?.contractValue) {
                    <span>{{ tender()?.contractValue | localeCurrency }}</span>
                }
            </div>
            <span class="details-label">{{
                'tender.details.maximumBudget.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.maximumBudget }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.deliveryLocation.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.deliveryLocation }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.winningCriteria.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.winningCriteria }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.weightingPriceQuality.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{ tender()?.weightingPriceQuality }}</span>
            </div>
            <span class="details-label">{{
                'tender.details.bindingDeadline.label' | translate
            }}</span>
            <div class="details-value">
                <span>{{
                    tender()?.bindingDeadline | localeDate: 'mediumDate'
                }}</span>
            </div>
        </div>
    </div>

    <app-separator separatorType="horizontal"></app-separator>
    <div class="w-full p-6">
        <mat-accordion>
            <mat-expansion-panel>
                <mat-expansion-panel-header>
                    <span class="details-label">{{
                        'tender.description.description-input.label' | translate
                    }}</span>
                </mat-expansion-panel-header>
                <div class="details-value">
                    <textarea
                        cdkTextareaAutosize
                        cdkAutosizeMinRows="10"
                        cdkAutosizeMaxRows="20"
                        style="width: 100%; resize: none"
                        disabled
                        >{{ tender()?.description }}</textarea
                    >
                </div></mat-expansion-panel
            ></mat-accordion
        >
    </div>

    <app-separator separatorType="horizontal"></app-separator>
    <div class="w-full p-6">
        <mat-accordion>
            <mat-expansion-panel>
                <mat-expansion-panel-header>
                    <span class="details-label">{{
                        'tender.details.comment.label' | translate
                    }}</span>
                </mat-expansion-panel-header>
                <div class="details-value">
                    <textarea
                        cdkTextareaAutosize
                        cdkAutosizeMinRows="3"
                        cdkAutosizeMaxRows="10"
                        style="width: 100%; resize: none"
                        disabled
                        >{{ tender()?.comment }}</textarea
                    >
                </div>
            </mat-expansion-panel>
        </mat-accordion>
    </div>

    <app-separator separatorType="horizontal"></app-separator>
    <div class="w-full p-6">
        <div class="grid grid-cols-2 gap-4">
            <span class="details-label">{{
                'tender.details.rating.label' | translate
            }}</span>
            <div class="details-value">
                <div class="rating flex items-center">
                    @for (
                        icon of tender()?.rating ?? 0 | starIcons: 5;
                        track $index
                    ) {
                        <mat-icon>{{ icon }}</mat-icon>
                    }
                </div>
            </div>

            <span class="details-label">{{
                'tender.details.isFavorite.label' | translate
            }}</span>
            <div class="details-value">
                <mat-icon>{{
                    tender()?.isFavorite ? 'favorite' : 'favorite_border'
                }}</mat-icon>
                <span class="ml-2">{{
                    tender()?.isFavorite
                        ? ('common.yes' | translate)
                        : ('common.no' | translate)
                }}</span>
            </div>
        </div>
    </div>

    <app-separator separatorType="horizontal"></app-separator>
    <form [formGroup]="tenderDetailsFileForm" class="w-full p-6">
        <app-tender-file-load
            formControlName="files"
            [autoUpload]="true"
            [tenderId]="tender()?.id ?? ''"
            [label]="'tender.documents.header' | translate"
        >
        </app-tender-file-load>
    </form>
</div>
