import { CdkTextareaAutosize } from '@angular/cdk/text-field'
import { Component, effect, inject, input, signal } from '@angular/core'
import { FormBuilder, ReactiveFormsModule } from '@angular/forms'
import {
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
} from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'

import { TenderDTO } from '@app/api'
import { LoggerService } from '@app/core/logging/logger.service'
import { TenderFileLoadComponent } from '@modules/tenders/components/tender-file/tender-file-load.component'
import { TenderFileFormModel } from '@modules/tenders/models/tender.model'
import {
    buildTenderFileForm,
    mapTenderDTOToTenderFileFormModel,
} from '@modules/tenders/models/tender-form.factory'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { LocaleCurrencyPipe } from '@shared/utils/custom-pipes/locale-currency.pipe'
import { LocaleDatePipe } from '@shared/utils/custom-pipes/locale-date.pipe'
import { StarIconsPipe } from '@shared/utils/custom-pipes/star-icons.pipe'

import { TranslateModule } from '@ngx-translate/core'

@Component({
    selector: 'app-tender-details',
    imports: [
        ReactiveFormsModule,
        TranslateModule,
        SeparatorComponent,
        LocaleDatePipe,
        MatAccordion,
        MatExpansionPanelHeader,
        MatExpansionPanel,
        TenderFileLoadComponent,
        CdkTextareaAutosize,
        LocaleCurrencyPipe,
        MatIcon,
        StarIconsPipe,
    ],
    templateUrl: './tender-details.component.html',
    styleUrl: './tender-details.component.scss',
    standalone: true,
})
export class TenderDetailsComponent {
    tenderDetailsFileForm = buildTenderFileForm(inject(FormBuilder))

    protected formValid = signal<boolean>(false)
    public isDirty = signal<boolean>(false)
    public tender = input<TenderDTO | null>(null)

    constructor(private logger: LoggerService) {
        this.tenderDetailsFileForm.valueChanges.subscribe(() => {
            this.isDirty.set(this.tenderDetailsFileForm.dirty)
            this.formValid.set(this.tenderDetailsFileForm.valid)
        })

        effect(() => {
            if (!this.tender) {
                this.logger.warn('No tender provided.')
                return
            }
            this.resetForm(this.tender())
        })
    }

    resetForm(tender: TenderDTO | null) {
        if (tender) {
            const formModel: TenderFileFormModel =
                mapTenderDTOToTenderFileFormModel(tender)

            this.tenderDetailsFileForm.reset(formModel)
            this.tenderDetailsFileForm.markAsPristine()
        }
    }
}
