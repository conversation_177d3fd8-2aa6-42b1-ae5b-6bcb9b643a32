<div
    class="drag-and-drop w-full h-full flex flex-col gap-verticalGap"
    appDragAndDrop
    (fileDropped)="addFiles($event)"
>
    <div class="w-full flex justify-between items-end gap-x-horizontalPad">
        <div class="min-w-40 max-w-72 flex-1">
            <label for="filter-documents">{{
                'tender.documents.search.label' | translate
            }}</label>
            <app-input-field-text
                #fileFilterInputField
                inputId="filter-documents"
                (valueChange)="changeFilter($event)"
                [placeholder]="
                    'tender.documents.search.placeholder' | translate
                "
            ></app-input-field-text>
        </div>
        @if (fileUploadProgress) {
            <mat-progress-bar
                class="flex-1"
                mode="determinate"
                [value]="fileUploadProgress"
            ></mat-progress-bar>
        }

        <app-button
            class="flex-grow-0"
            icon="add"
            (buttonClick)="documentFileInput.click()"
            >{{ 'tender.documents.add-button' | translate }}</app-button
        >
        <input
            #documentFileInput
            class="hidden"
            type="file"
            id="document-file-input"
        />
        <!--            (change)="onFileChange($event)"-->
    </div>
    <div class="w-full h-full">
        <app-tender-file-table></app-tender-file-table>
    </div>
</div>
