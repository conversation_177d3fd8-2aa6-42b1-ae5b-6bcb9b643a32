import {
    AfterViewInit,
    Component,
    effect,
    input,
    output,
    viewChild,
} from '@angular/core'
import { MatProgressBar } from '@angular/material/progress-bar'

import { TenderDTO } from '@app/api'
import { LoggerService } from '@app/core/logging/logger.service'
import { ButtonComponent } from '@app/shared/components/button/button.component'
import { InputFieldTextComponent } from '@app/shared/components/input-field-text/input-field-text.component'
import { DragAndDropDirective } from '@app/shared/directives/drag-and-drop.directive'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { PopUpService } from '@shared/services/pop-up.service'

import { TenderService } from '../../services/tender.service'
import { TenderFileTableComponent } from '../tender-file-table/tender-file-table.component'

import { TranslateModule } from '@ngx-translate/core'

@Component({
    selector: 'app-tender-documents',
    imports: [
        ButtonComponent,
        DragAndDropDirective,
        InputFieldTextComponent,
        MatProgressBar,
        TranslateModule,
        TenderFileTableComponent,
    ],
    templateUrl: './tender-documents.component.html',
    styleUrl: './tender-documents.component.scss',
    standalone: true,
})
export class TenderDocumentsComponent implements AfterViewInit {
    public tender = input<TenderDTO>()
    public updateTender = output()

    fileUploadProgress: undefined | number = undefined
    tenderFileTable = viewChild<TenderFileTableComponent>(
        TenderFileTableComponent
    )
    fileFilterInputField = viewChild<InputFieldTextComponent>(
        'fileFilterInputField'
    )

    queryParamStore = createURLVariables('documentsFilter')

    constructor(
        private tenderService: TenderService,
        private popUpService: PopUpService,
        private logger: LoggerService
    ) {
        effect(() => {
            if (this.fileFilterInputField())
                this.fileFilterInputField()!.value =
                    this.queryParamStore.get().documentsFilter ?? ''
        })
    }

    // Why ngAfterViewInit? if used before: the datasource of the table is not initialized
    ngAfterViewInit(): void {
        if (this.queryParamStore.get().documentsFilter != undefined)
            this.changeFilter(this.queryParamStore.get().documentsFilter ?? '')
    }

    changeFilter(filter: string) {
        this.tenderFileTable()?.changeFilter(filter)
        this.queryParamStore.update((params) => {
            params.documentsFilter = filter
            return params
        })
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    addFiles(files: FileList) {
        if (!this.tender()?.id) return

        // for (const file of files) {
        // const newFile: Omit<TenderFile, 'uploadDateTime'> = {
        //     filename: `${file.name}_${new Date().toISOString()}`,
        //     size: file.size,
        //     format: file.type,
        // }
        //     this.tenderService
        //         .addFileToTender(this.tender()!.id!, newFile)
        //         .subscribe({
        //             next: (progress) => {
        //                 this.fileUploadProgress = progress
        //             },
        //             complete: () => {
        //                 setTimeout(() => {
        //                     this.fileUploadProgress = undefined
        //                     this.tenderFileTable()?.fetchFiles() //Workaround because localstorage modifications are not instant apparently
        //                 }, 1000)
        //
        //                 this.popUpService.openSuccessPopUp(
        //                     'Ok',
        //                     'Upload was successful',
        //                     3000
        //                 )
        //
        //                 this.updateTender.emit()
        //             },
        //             error: (err) => {
        //                 this.logger.error(
        //                     'Error while adding document to tender: ',
        //                     err
        //                 )
        //                 setTimeout(() => {
        //                     this.fileUploadProgress = undefined
        //                 }, 1000)
        //                 this.popUpService.openErrorPopUp(
        //                     'Ok',
        //                     'Upload did not succeed',
        //                     3000
        //                 )
        //             },
        //         })
        // }
        // }

        // onFileChange(event: Event) {
        //     const inputElement = event.target as HTMLInputElement
        //
        //     if (inputElement.files && inputElement.files.length > 0) {
        //         const files: FileList = inputElement.files
        //         this.addFiles(files)
        //     } else {
        //         this.logger.warn('No files selected.')
        //     }
    }
}
