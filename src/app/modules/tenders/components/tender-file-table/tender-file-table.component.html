<div class="content">
    <table mat-table [dataSource]="dataSource" matSort>
        <ng-container matColumnDef="filename">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.documents.table.name' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">{{ element.filename }}</td>
        </ng-container>

        <ng-container matColumnDef="size">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.documents.table.size' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.size }}
            </td>
        </ng-container>

        <ng-container matColumnDef="uploadDateTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.documents.table.created-at' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.uploadDateTime | date }}
            </td>
        </ng-container>

        <ng-container matColumnDef="format">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
                {{ 'tender.documents.table.format' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                {{ element.format }}
            </td>
        </ng-container>

        <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
                {{ 'tender.documents.table.actions' | translate }}
            </th>
            <td mat-cell *matCellDef="let element">
                <app-button
                    (buttonClick)="
                        $event.stopPropagation(); onFileDelete(element)
                    "
                >
                    <span class="material-symbols-outlined"> delete </span>
                </app-button>
                <app-button
                    class="px-1"
                    (buttonClick)="
                        $event.stopPropagation(); downloadDocument(element)
                    "
                >
                    <span class="material-symbols-outlined"> download </span>
                </app-button>
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr
            mat-row
            (click)="downloadDocument(row)"
            *matRowDef="let row; columns: displayedColumns"
        ></tr>
    </table>

    <mat-paginator
        showFirstLastButtons
        aria-label="Select page of periodic elements"
        pageSize="10"
    >
    </mat-paginator>
</div>
