import { CommonModule } from '@angular/common'
import {
    AfterViewInit,
    Component,
    inject,
    OnInit,
    ViewChild,
} from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator'
import { MatSort, MatSortModule } from '@angular/material/sort'
import { MatTableDataSource, MatTableModule } from '@angular/material/table'
import { ActivatedRoute } from '@angular/router'

import { TenderFile } from '@app/modules/tenders/models/tender.model'
import { TenderService } from '@app/modules/tenders/services/tender.service'
import { LoggerService } from '@core/logging/logger.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '@shared/components/confirm-dialog/confirm-dialog.component'

import { TranslateModule, TranslateService } from '@ngx-translate/core'
import { firstValueFrom } from 'rxjs'

@Component({
    selector: 'app-tender-file-table',
    imports: [
        MatTableModule,
        CommonModule,
        MatFormFieldModule,
        MatSortModule,
        MatPaginatorModule,
        TranslateModule,
        ButtonComponent,
    ],
    templateUrl: './tender-file-table.component.html',
    styleUrl: './tender-file-table.component.scss',
    standalone: true,
})
export class TenderFileTableComponent implements AfterViewInit, OnInit {
    @ViewChild(MatSort) sort!: MatSort
    @ViewChild(MatPaginator) paginator!: MatPaginator

    private readonly translate = inject(TranslateService)

    tenderFiles: TenderFile[] | undefined = undefined
    dataSource!: MatTableDataSource<TenderFile, MatPaginator>
    getTenderId = () => this.route.snapshot.parent?.data['tenderId']

    displayedColumns: string[] = [
        'filename',
        'size',
        'uploadDateTime',
        'format',
        'actions',
    ]
    readonly dialog = inject(MatDialog)

    constructor(
        private tenderService: TenderService,
        private logger: LoggerService,
        private route: ActivatedRoute
    ) {}

    ngAfterViewInit() {
        this.dataSource.sort = this.sort
        this.dataSource.paginator = this.paginator
    }

    ngOnInit(): void {
        this.fetchFiles()
    }

    public fetchFiles() {
        // const tenderId = this.getTenderId()
        // if (!tenderId) {
        //     this.logger.warn('No Selected tender in tender-file-table')
        //     return
        // }
        // this.tenderService.getTenderById(tenderId).subscribe((tender) => {
        //     this.tenderFiles = tender?.files
        //     if (!this.dataSource)
        //         this.dataSource = new MatTableDataSource(this.tenderFiles)
        //     this.dataSource.data = this.tenderFiles ?? []
        //     this.dataSource._updateChangeSubscription()
        // })
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public async onFileDelete(tenderFile: TenderFile) {
        const tenderId = this.getTenderId()

        if (!tenderId) {
            this.logger.warn('No Selected tender in tender-file-table')
            return
        }

        const header = this.translate.instant(
            'confirm-dialog.delete-document.header'
        )
        const text = this.translate.instant(
            'confirm-dialog.delete-document.text'
        )

        const dialogData: ConfirmDialogData = {
            header: header,
            text: text,
        }

        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            data: dialogData,
        })

        const shouldContinue: boolean = await firstValueFrom(
            dialogRef.afterClosed()
        )
        if (!shouldContinue) return

        // this.tenderService
        //     .removeFileFromTender(tenderId, tenderFile.filename)
        //     .subscribe({
        //         next: (success) => {
        //             if (success) {
        //                 this.fetchFiles()
        //             } else {
        //                 alert('File delete was not successful')
        //                 this.logger.error('File delete was not successful')
        //             }
        //         },
        //         error: (err) => {
        //             alert('Something went wrong')
        //             this.logger.error(
        //                 'Error when removing file from tender: ',
        //                 err
        //             )
        //         },
        //     })
    }

    public changeFilter(filter: string) {
        if (!this.dataSource) {
            this.logger.warn('changed dataSource.filter too early')
            return
        }

        this.dataSource.filter = filter
    }

    downloadDocument(tenderFile: TenderFile) {
        alert(`Downloading Document: ${tenderFile.filename}`)
    }
}
