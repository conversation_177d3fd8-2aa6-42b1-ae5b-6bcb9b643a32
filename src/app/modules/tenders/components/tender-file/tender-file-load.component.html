<div class="flex flex-col gap-4 file-list-container">
    <label class="mt-2 mb-1" [for]="inputId()">{{ label() }}</label>
    <div class="flex justify-between items-center gap-4">
        <div
            class="flex-1 border-2 border-dashed border-gray-300 p-4 text-center rounded drop-zone"
            appDragAndDrop
            (fileDropped)="onFileDropped($event)"
            (click)="fileInput.click()"
            tabindex="0"
            (keydown.enter)="fileInput.click()"
        >
            <p>
                {{ 'tender.documents.drag-and-drop-files-here' | translate }}
            </p>
            <mat-icon class="text-4xl mt-2 material-symbols-outlined"
                >cloud_upload</mat-icon
            >
        </div>
        <input
            #fileInput
            [id]="inputId()"
            type="file"
            multiple
            [accept]="getAcceptedFiles()"
            (change)="onFileSelected($event)"
            hidden
        />
    </div>
    @if (files.length + metadataFiles.length > 0) {
        <div class="flex justify-start items-center gap-4">
            <app-button
                (buttonClick)="deleteSelected()"
                icon="delete"
                variant="secondary"
                class="tender-file-action-button"
                [disabled]="!anyFileSelected()"
            >
                {{ 'tender.documents.delete-selected-files' | translate }}
            </app-button>
            <app-button
                (buttonClick)="downloadAllSelected()"
                icon="folder_zip"
                variant="secondary"
                class="tender-file-action-button"
                [disabled]="
                    files.length + metadataFiles.length < 2 ||
                    !anyFileSelected()
                "
            >
                {{ 'tender.documents.download-all-files-as-zip' | translate }}
            </app-button>
        </div>

        <div #fileList class="tender-file-list">
            <mat-list>
                <!-- already uploaded files -->
                @if (metadataFiles.length) {
                    <ng-container>
                        <div class="metadata-files-container">
                            <mat-list-item
                                ><h4>
                                    {{
                                        'tender.documents.uploaded-files-title'
                                            | translate
                                    }}
                                </h4></mat-list-item
                            >
                            <mat-list-item>
                                <mat-checkbox
                                    class="tender-file-action-button select-all-cbx mb-2"
                                    [checked]="isAllMetadataFilesSelected()"
                                    [indeterminate]="
                                        isIndeterminateMetadataFiles()
                                    "
                                    (change)="
                                        toggleAllMetadataFiles($event.checked)
                                    "
                                    [disabled]="metadataFiles.length < 2"
                                    >{{
                                        'tender.documents.select-all'
                                            | translate
                                    }}</mat-checkbox
                                >
                            </mat-list-item>
                            @for (
                                metadataFileItem of metadataFiles;
                                track metadataFileItem.metadata.id
                            ) {
                                <mat-list-item>
                                    <div
                                        class="mat-list-item-content gap-2 items-center w-full"
                                    >
                                        <mat-checkbox
                                            [(ngModel)]="
                                                metadataFileItem.selected
                                            "
                                        ></mat-checkbox>
                                        <mat-icon
                                            class="disabled-color"
                                            [svgIcon]="
                                                getIconForFile(
                                                    metadataFileItem.metadata
                                                        .fileName
                                                )
                                            "
                                        ></mat-icon>
                                        @if (
                                            getFileIcon(metadataFileItem);
                                            as iconData
                                        ) {
                                            <ng-container>
                                                <mat-icon
                                                    [color]="iconData.color"
                                                    [ngClass]="iconData.class"
                                                    [matTooltip]="
                                                        iconData.tooltip
                                                            ? (iconData.tooltip
                                                              | translate)
                                                            : ''
                                                    "
                                                    >{{ iconData.icon }}
                                                </mat-icon>
                                            </ng-container>
                                        }
                                        <div
                                            class="truncate"
                                            [matTooltip]="
                                                metadataFileItem.metadata
                                                    .fileName
                                            "
                                            [matTooltipPosition]="'above'"
                                        >
                                            <span
                                                class="font-medium file-name"
                                                >{{
                                                    metadataFileItem.metadata
                                                        .fileName
                                                }}</span
                                            >
                                        </div>
                                        <span
                                            class="whitespace-nowrap text-sm disabled-color"
                                            >{{
                                                metadataFileItem.metadata
                                                    .fileSizeBytes / 1024
                                                    | number: '1.0-2'
                                            }}
                                            kB</span
                                        >
                                        <app-button
                                            variant="plain"
                                            (buttonClick)="
                                                downloadMetadataFile(
                                                    metadataFileItem
                                                )
                                            "
                                        >
                                            <mat-icon
                                                class="material-symbols-outlined"
                                                >download</mat-icon
                                            >
                                        </app-button>
                                        <app-button
                                            variant="plain"
                                            (buttonClick)="
                                                deleteMetadataFile(
                                                    metadataFileItem
                                                )
                                            "
                                        >
                                            <mat-icon
                                                class="material-symbols-outlined"
                                                >delete</mat-icon
                                            >
                                        </app-button>
                                    </div>
                                    @if (
                                        metadataFileItem.status ===
                                            FileStatus.Deleting ||
                                        metadataFileItem.status ===
                                            FileStatus.Uploading
                                    ) {
                                        <ng-container class="mt-2">
                                            <mat-progress-bar
                                                mode="indeterminate"
                                                [value]="
                                                    metadataFileItem.progress
                                                "
                                            >
                                            </mat-progress-bar>
                                        </ng-container>
                                    }
                                </mat-list-item>
                            }
                        </div>
                    </ng-container>
                }
                <!-- new (uploaded) files -->
                @if (files.length) {
                    <app-separator separatorType="horizontal"></app-separator>
                    <ng-container>
                        <mat-list-item class="files-container"
                            ><h4>
                                {{
                                    'tender.documents.new-files-title'
                                        | translate
                                }}
                            </h4></mat-list-item
                        >
                        <mat-list-item>
                            <div
                                class="flex flex-row justify-start items-center gap-8"
                            >
                                <mat-checkbox
                                    class="tender-file-action-button select-all-cbx mb-2"
                                    [checked]="isAllFilesSelected()"
                                    [indeterminate]="isIndeterminateFiles()"
                                    (change)="toggleAllFiles($event.checked)"
                                    [disabled]="isAllFilesCbxActive()"
                                    >{{
                                        'tender.documents.select-all'
                                            | translate
                                    }}</mat-checkbox
                                >
                            </div></mat-list-item
                        >
                        @for (fileItem of files; track $index) {
                            <mat-list-item>
                                <div
                                    class="mat-list-item-content gap-2 items-center w-full"
                                >
                                    <mat-checkbox
                                        [(ngModel)]="fileItem.selected"
                                        [disabled]="
                                            fileItem.status ===
                                            FileStatus.SuccessUpload
                                        "
                                    ></mat-checkbox>
                                    <mat-icon
                                        [svgIcon]="
                                            getIconForFile(fileItem.file)
                                        "
                                    ></mat-icon>
                                    @if (getFileIcon(fileItem); as iconData) {
                                        <ng-container>
                                            <mat-icon
                                                [color]="iconData.color"
                                                [ngClass]="iconData.class"
                                                [matTooltip]="
                                                    iconData.tooltip
                                                        ? (iconData.tooltip
                                                          | translate)
                                                        : ''
                                                "
                                                >{{ iconData.icon }}
                                            </mat-icon>
                                        </ng-container>
                                    }
                                    <div
                                        class="truncate"
                                        [matTooltip]="fileItem.file.name"
                                        [matTooltipPosition]="'above'"
                                    >
                                        <span class="font-medium file-name">{{
                                            fileItem.file.name
                                        }}</span>
                                    </div>
                                    @if (fileItem.file && fileItem.file.size) {
                                        <span
                                            class="whitespace-nowrap text-gray-600 text-sm"
                                            >{{
                                                fileItem.file.size / 1024
                                                    | number: '1.0-2'
                                            }}
                                            kB</span
                                        >
                                    }
                                    @if (showFileActionButtons()) {
                                        <app-button
                                            variant="plain"
                                            (buttonClick)="addFile(fileItem)"
                                            [disabled]="
                                                fileItem.status ===
                                                    FileStatus.SuccessUpload ||
                                                fileItem.status ===
                                                    FileStatus.Uploading
                                            "
                                        >
                                            <mat-icon
                                                class="material-symbols-outlined"
                                                >publish</mat-icon
                                            >
                                        </app-button>
                                    }
                                    <app-button
                                        variant="plain"
                                        (buttonClick)="deleteFile(fileItem)"
                                        [disabled]="
                                            fileItem.status ===
                                                FileStatus.SuccessUpload ||
                                            fileItem.status ===
                                                FileStatus.Uploading
                                        "
                                    >
                                        <mat-icon
                                            class="material-symbols-outlined"
                                            >delete</mat-icon
                                        >
                                    </app-button>
                                </div>
                                @if (
                                    fileItem.status === FileStatus.Uploading ||
                                    fileItem.status === FileStatus.Downloading
                                ) {
                                    <ng-container>
                                        <mat-progress-bar
                                            mode="indeterminate"
                                            [value]="fileItem.progress"
                                        >
                                        </mat-progress-bar>
                                    </ng-container>
                                }
                            </mat-list-item>
                        }
                    </ng-container>
                }
            </mat-list>
        </div>
    }
</div>
