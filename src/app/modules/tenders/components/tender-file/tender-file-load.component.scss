.drop-zone {
    border: 2px dashed var(--disabled);
    padding: 0.5rem;
    text-align: center;
    transition: background-color 0.2s ease;
    cursor: pointer;

    .mat-icon {
        width: 34px;
        height: 34px;
    }

    &:hover {
        outline: 1px solid var(--primary-highlighted-color);
    }
}

.drop-zone.highlight {
    background-color: #f5f5f5;
}

.tender-file-action-button.select-all-cbx {
    margin-top: var(--mat-form-field-container-vertical-padding, 16px);
}

.tender-file-list {
    border: 1px solid var(--disabled);
    border-radius: 2px;

    &:hover {
        outline: 1px solid var(--primary-highlighted-color);
    }

    .mat-list-item-content {
        display: grid;
        grid-template-columns: auto auto auto 2fr 1fr auto auto;

        ::ng-deep button {
            margin: 0;
        }
    }
}

.metadata-files-container h4,
.files-container h4 {
    font-family: Messina-Regular, sans-serif;
    color: var(--primary-color);
}
