import { Decimal<PERSON>ipe, NgClass } from '@angular/common'
import { HttpEventType } from '@angular/common/http'
import {
    ChangeDetectorRef,
    Component,
    ElementRef,
    forwardRef,
    inject,
    input,
    viewChild,
} from '@angular/core'
import { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms'
import { MatCheckbox } from '@angular/material/checkbox'
import { MatDialog } from '@angular/material/dialog'
import { MatIcon } from '@angular/material/icon'
import { MatList, MatListItem } from '@angular/material/list'
import { MatProgressBar } from '@angular/material/progress-bar'
import { MatTooltip } from '@angular/material/tooltip'

import { FileMetadataDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'
import {
    FileStatus,
    TenderCombinedFileValue,
    TenderFileItem,
    TenderMetadataFileItem,
} from '@modules/tenders/models/tender-file.model'
import { TenderFileService } from '@modules/tenders/services/tender-file.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '@shared/components/confirm-dialog/confirm-dialog.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { DragAndDropDirective } from '@shared/directives/drag-and-drop.directive'
import { PopUpService } from '@shared/services/pop-up.service'
import {
    getAcceptedFiles,
    getAllowedFileConfig,
    getFileIcon,
    getIconForFile,
    isAllowedFile,
} from '@shared/utils/file-utils'

import { TranslatePipe, TranslateService } from '@ngx-translate/core'
import { saveAs } from 'file-saver'
import JSZip from 'jszip'
import { firstValueFrom } from 'rxjs'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10 MB in bytes

@Component({
    selector: 'app-tender-file-load',
    imports: [
        DragAndDropDirective,
        MatIcon,
        ButtonComponent,
        TranslatePipe,
        DecimalPipe,
        MatCheckbox,
        MatList,
        MatListItem,
        FormsModule,
        MatTooltip,
        MatProgressBar,
        SeparatorComponent,
        NgClass,
    ],
    templateUrl: './tender-file-load.component.html',
    styleUrl: './tender-file-load.component.scss',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => TenderFileLoadComponent),
            multi: true,
        },
    ],
    standalone: true,
})
export class TenderFileLoadComponent {
    private readonly translate = inject(TranslateService)
    readonly dialog = inject(MatDialog)

    inputId = input('')
    label = input('')
    tenderId = input('')
    disabled = input<boolean>(false)
    autoUpload = input<boolean>(false)
    showFileActionButtons = input<boolean>(true)

    files: TenderFileItem[] = [] // new files
    metadataFiles: TenderMetadataFileItem[] = [] // existing files

    fileInput = viewChild<ElementRef>('fileInput')
    fileList = viewChild<ElementRef>('fileList')

    private onChange: (
        value: TenderCombinedFileValue | TenderFileItem[]
    ) => void = () => undefined
    private onTouched: () => void = () => undefined

    constructor(
        private cdr: ChangeDetectorRef,
        private logger: LoggerService,
        private tenderFileService: TenderFileService,
        private popUpService: PopUpService
    ) {}

    private updateProgress(fileItem: any, event: any): void {
        if (event.total) {
            this.logger.log(`updateProgress: ${fileItem.progress}%`)
            fileItem.progress = Math.round((100 * event.loaded) / event.total)
        }
    }

    private async showDeleteDialog(
        headerKey: string,
        textKey: string,
        params?: any
    ): Promise<boolean> {
        const dialogData: ConfirmDialogData = {
            header: this.translate.instant(headerKey),
            text: this.translate.instant(textKey, params),
        }
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            data: dialogData,
        })
        return await firstValueFrom(dialogRef.afterClosed())
    }

    private scrollToFileList() {
        setTimeout(() => {
            this.fileList()?.nativeElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
            })
        }, 100)
    }

    anyFileSelected() {
        return (
            this.files.some((f) => f.selected) ||
            this.metadataFiles.some((f) => f.selected)
        )
    }

    onFileSelected(event: Event): void {
        const input = event.target as HTMLInputElement
        if (input.files && input.files.length) {
            Array.from(input.files).forEach((file) => this.processFile(file))
            input.value = ''
            this.scrollToFileList()
        }
    }

    onFileDropped(files: FileList): void {
        if (files && files.length) {
            Array.from(files).forEach((file) => this.processFile(file))
            this.scrollToFileList()
        }
    }

    async processFile(file: File): Promise<void> {
        if (file.name.endsWith('.zip')) {
            const zip = new JSZip()
            const content = await zip.loadAsync(file)
            for (const filename in content.files) {
                const zipEntry = content.files[filename]
                if (!zipEntry.dir) {
                    const extractedBlob = await zipEntry.async('blob')
                    const extractedFile = new File([extractedBlob], filename, {
                        type: extractedBlob.type,
                    })
                    if (filename.toLowerCase().endsWith('.zip')) {
                        // extract nested zip file
                        await this.processFile(extractedFile)
                    } else {
                        await this.processSingleFile(extractedFile)
                    }
                }
            }
        } else {
            await this.processSingleFile(file)
        }
        this.updateFiles()
    }

    private async processSingleFile(file: File): Promise<void> {
        if (file.size > MAX_FILE_SIZE) {
            const tooLargeItem: TenderFileItem = {
                file: file,
                tenderId: this.tenderId(),
                progress: 0,
                selected: false,
                status: FileStatus.TooLarge,
            }
            // shown in Browser but not (!) uploaded
            this.files.push(tooLargeItem)
            return
        }

        const allowedConfig = getAllowedFileConfig()
        if (!isAllowedFile(file, allowedConfig)) {
            this.popUpService.openErrorPopUp(
                this.translate.instant('popup.error.invalid-file-type')
            )
            return
        }
        const newFileItem: TenderFileItem = {
            file: file,
            tenderId: this.tenderId(),
            progress: 0,
            selected: false,
            status: FileStatus.Pending,
        }

        if (!this.isDuplicateFile(newFileItem.file)) {
            this.files.push(newFileItem)
            if (this.autoUpload()) {
                this.addFile(newFileItem)
            }
        } else {
            this.popUpService.openErrorPopUp(
                this.translate.instant('popup.error.duplicated-file-tender')
            )
        }
    }

    private isDuplicateFile(file: File): boolean {
        const duplicateNew = this.files.some(
            (existing) =>
                existing?.file?.name === file.name &&
                existing?.file.size === file.size
        )
        const duplicateMeta = this.metadataFiles.some(
            (existing) =>
                existing?.metadata?.fileName === file.name &&
                existing?.metadata.fileSizeBytes === file.size
        )

        if (duplicateNew || duplicateMeta) {
            const errorMessage = this.translate.instant(
                'popup.error.duplicated-file-tender'
            )
            this.popUpService.openErrorPopUp(errorMessage)
        }
        return duplicateNew || duplicateMeta
    }

    async deleteFile(
        fileItem: TenderFileItem,
        showDialog = true
    ): Promise<void> {
        if (showDialog) {
            const shouldContinue = await this.showDeleteDialog(
                'confirm-dialog.delete-file.header',
                'confirm-dialog.delete-file.text'
            )
            if (!shouldContinue) return
        }

        const index = this.files.findIndex(
            (f) => f.file?.name === fileItem.file?.name
        )
        if (index >= 0) {
            this.files.splice(index, 1)
            this.updateFiles()
        }
    }

    async deleteMetadataFile(
        fileItem: TenderMetadataFileItem,
        showDialog = true
    ): Promise<void> {
        if (showDialog) {
            const shouldContinue = await this.showDeleteDialog(
                'confirm-dialog.delete-file.header',
                'confirm-dialog.delete-file.text'
            )
            if (!shouldContinue) return
        }

        fileItem.status = FileStatus.Deleting
        this.tenderFileService
            .deleteTenderFile(fileItem.tenderId, fileItem.metadata.id)
            .subscribe({
                next: (event) => {
                    if (event.type === HttpEventType.UploadProgress) {
                        this.updateProgress(fileItem, event)
                    }
                },
                error: () => {
                    const errorMessage = this.translate.instant(
                        'popup.error.delete-file-tender'
                    )
                    this.popUpService.openErrorPopUp(errorMessage)

                    const index = this.metadataFiles.findIndex(
                        (f) => f.metadata?.id === fileItem.metadata?.id
                    )
                    if (index >= 0) {
                        this.metadataFiles[index].status =
                            FileStatus.ErrorDelete
                        this.updateFiles()
                    }
                },
                complete: () => {
                    const index = this.metadataFiles.findIndex(
                        (f) => f.metadata?.id === fileItem.metadata?.id
                    )
                    if (index >= 0) {
                        this.metadataFiles.splice(index, 1)
                        this.updateFiles()
                    }
                },
            })
    }

    async deleteSelected(): Promise<void> {
        const filesToDelete = this.files.filter((f) => f.selected)
        const metadataFilesToDelete = this.metadataFiles.filter(
            (f) => f.selected
        )

        const shouldContinue = await this.showDeleteDialog(
            'confirm-dialog.delete-files.header',
            'confirm-dialog.delete-files.text',
            { length: filesToDelete.length + metadataFilesToDelete.length }
        )
        if (!shouldContinue) return

        this.files = this.files.filter((f) => !f.selected)
        this.metadataFiles.forEach((metadataFileItem) => {
            if (metadataFileItem.selected) {
                void this.deleteMetadataFile(metadataFileItem, false)
            }
        })
        this.updateFiles()
    }

    addFile(fileItem: TenderFileItem): void {
        if (!fileItem.tenderId) {
            this.logger.warn('addFile: tenderId was undefined')
            return
        }

        fileItem.status = FileStatus.Uploading

        this.tenderFileService
            .addTenderFile(fileItem.tenderId, fileItem.file)
            .subscribe({
                next: (event) => {
                    if (event.type === HttpEventType.UploadProgress) {
                        this.updateProgress(fileItem, event)
                    } else if (event.type === HttpEventType.Response) {
                        const result: FileMetadataDTO = event.body!
                        this.metadataFiles.push({
                            metadata: result,
                            tenderId: fileItem.tenderId!,
                            progress: 100,
                            selected: false,
                            status: FileStatus.New,
                        })
                        fileItem.status = FileStatus.SuccessUpload
                        void this.deleteFile(fileItem, false)
                        this.updateFiles()
                    }
                },
                error: () => {
                    const errorMessage = this.translate.instant(
                        'popup.error.add-file-tender'
                    )
                    this.popUpService.openErrorPopUp(errorMessage)

                    fileItem.status = FileStatus.ErrorUpload
                    this.updateFiles()
                },
            })
    }

    uploadAll(): void {
        this.files
            .filter((f) => f.selected)
            .forEach((fileItem) => {
                if (fileItem.status !== FileStatus.SuccessUpload) {
                    fileItem.status = FileStatus.Uploading
                    this.addFile(fileItem)
                }
            })
    }

    downloadFile(fileItem: TenderFileItem): void
    downloadFile(blob: Blob, filename: string): void
    downloadFile(arg1: TenderFileItem | Blob, arg2?: string): void {
        let blob: Blob
        let filename: string

        if (arg1 instanceof Blob) {
            blob = arg1
            filename = arg2!
        } else {
            arg1.status = FileStatus.SuccessDownload
            blob = arg1.file
            filename = arg1.file.name
        }

        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        a.click()
        URL.revokeObjectURL(url)

        const successMessage = this.translate.instant(
            'popup.success.download-file'
        )
        this.popUpService.openSuccessPopUp(successMessage)
    }

    downloadMetadataFileAsBlob(
        fileItem: TenderMetadataFileItem
    ): Promise<Blob> {
        return new Promise((resolve, reject) => {
            this.tenderFileService
                .downloadTenderFile(fileItem.tenderId, fileItem.metadata.id)
                .subscribe({
                    next: (event) => {
                        if (event.type === HttpEventType.DownloadProgress) {
                            this.updateProgress(fileItem, event)
                        } else if (event.type === HttpEventType.Response) {
                            fileItem.status = FileStatus.SuccessDownload
                            resolve(event.body as Blob)
                        }
                    },
                    error: (err) => {
                        fileItem.status = FileStatus.ErrorDownload
                        reject(err)
                    },
                })
        })
    }

    downloadMetadataFile(fileItem: TenderMetadataFileItem): void {
        if (!fileItem.tenderId) {
            this.logger.warn('downloadFile: tenderId was undefined')
            return
        }

        fileItem.status = FileStatus.Downloading
        this.downloadMetadataFileAsBlob(fileItem)
            .then((blob) => {
                this.downloadFile(blob, fileItem.metadata.fileName)
                this.updateFiles()
            })
            .catch(() => {
                const errorMessage = this.translate.instant(
                    'popup.error.download-file-tender'
                )
                this.popUpService.openErrorPopUp(errorMessage)
                this.updateFiles()
            })
    }

    downloadAllSelected(): void {
        const zip = new JSZip()
        this.files
            .filter((f) => f.selected)
            .forEach((fileItem) => {
                if (!fileItem.file) {
                    return
                }
                fileItem.status = FileStatus.Downloading
                zip.file(fileItem.file.name, fileItem.file)
            })

        const metadataFilePromises = this.metadataFiles
            .filter((f) => f.selected)
            .map(async (fileItem) => {
                fileItem.status = FileStatus.Downloading
                return this.downloadMetadataFileAsBlob(fileItem).then(
                    (blob) => {
                        zip.file(fileItem.metadata.fileName, blob)
                    }
                )
            })

        Promise.all(metadataFilePromises)
            .then(() => {
                zip.generateAsync({ type: 'blob' }).then((blob) => {
                    saveAs(blob, 'all-files.zip')
                    this.files
                        .filter((f) => f.selected)
                        .forEach((fileItem) => {
                            fileItem.status = FileStatus.SuccessDownload
                        })

                    const successMessage = this.translate.instant(
                        'popup.success.download-files-zip'
                    )
                    this.popUpService.openSuccessPopUp(successMessage)
                })
            })
            .catch((error) => {
                this.logger.error(
                    'Error when trying to download selected files as zip: ',
                    error
                )

                this.files
                    .filter((f) => f.selected)
                    .forEach((fileItem) => {
                        fileItem.status = FileStatus.ErrorDownload
                    })

                const errorMessage = this.translate.instant(
                    'popup.error.download-files-zip'
                )
                this.popUpService.openErrorPopUp(errorMessage)
            })
    }

    updateFiles() {
        this.onChange({
            newFiles: this.files,
            metadataFiles: this.metadataFiles,
        })
    }

    isAllFilesSelected(): boolean {
        const relevantFiles = this.files.filter(
            (f) => f.status !== FileStatus.SuccessUpload
        )
        return (
            relevantFiles.length > 0 && relevantFiles.every((f) => f.selected)
        )
    }

    isAllFilesCbxActive(): boolean {
        const relevantFiles = this.files.filter(
            (f) => f.status !== FileStatus.SuccessUpload
        )
        return relevantFiles.length < 2
    }

    isAllMetadataFilesSelected(): boolean {
        return (
            this.metadataFiles.length > 0 &&
            this.metadataFiles.every((f) => f.selected)
        )
    }

    isIndeterminateFiles(): boolean {
        const relevantFiles = this.files.filter(
            (f) => f.status !== FileStatus.SuccessUpload
        )
        return (
            relevantFiles.some((f) => f.selected) && !this.isAllFilesSelected()
        )
    }

    isIndeterminateMetadataFiles(): boolean {
        const anySelected = this.metadataFiles.some((f) => f.selected)
        return anySelected && !this.isAllMetadataFilesSelected()
    }

    toggleAllFiles(checked: boolean): void {
        const relevantFiles = this.files.filter(
            (f) => f.status !== FileStatus.SuccessUpload
        )
        relevantFiles.forEach((f) => (f.selected = checked))
        this.updateFiles()
    }

    toggleAllMetadataFiles(checked: boolean): void {
        this.metadataFiles.forEach((f) => (f.selected = checked))
        this.updateFiles()
    }

    writeValue(value: TenderCombinedFileValue | TenderFileItem[]): void {
        if (value) {
            if (Array.isArray(value)) {
                this.files = value as TenderFileItem[]
                this.files.forEach((f) => (f.status = FileStatus.Pending))
                this.metadataFiles = []
            } else {
                this.files = value.newFiles || []
                this.metadataFiles = value.metadataFiles || []
                this.metadataFiles.forEach(
                    (f) => (f.status = FileStatus.Uploaded)
                )
            }
        } else {
            this.files = []
            this.metadataFiles = []
        }
        this.cdr.markForCheck()
    }

    registerOnChange(
        fn: (value: TenderFileItem[] | TenderCombinedFileValue) => void
    ): void {
        this.onChange = fn
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn
    }

    protected readonly getIconForFile = getIconForFile
    protected readonly getFileIcon = getFileIcon
    protected readonly FileStatus = FileStatus
    protected readonly getAcceptedFiles = getAcceptedFiles
}
