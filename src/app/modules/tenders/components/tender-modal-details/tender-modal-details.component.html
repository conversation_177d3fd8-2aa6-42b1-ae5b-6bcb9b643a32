<app-modal
    [title]="getHeader() | translate"
    [disableSave]="
        mode === 'edit'
            ? !(tenderUpdateComponent?.formValid ?? false) ||
              !(tenderUpdateComponent?.isDirty() ?? false)
            : false
    "
    [useDefaultButtons]="mode === 'edit'"
    (saveClicked)="onSave()"
    (cancelClicked)="onCancel()"
    (closeClicked)="onClose()"
>
    <div headerButtons class="flex gap-8">
        @if (mode !== 'analysis') {
            <app-button
                variant="plain"
                class="no-mt"
                (buttonClick)="mode === 'edit' ? onCancel() : (mode = 'edit')"
            >
                @if (mode === 'edit') {
                    <mat-icon class="material-symbols-outlined"
                        >edit_off</mat-icon
                    >
                    <span>{{
                        'tender.edit-modal.edit-mode-off-button' | translate
                    }}</span>
                } @else {
                    <mat-icon class="material-symbols-outlined">edit</mat-icon>
                    <span>{{
                        'tender.edit-modal.edit-mode-on-button' | translate
                    }}</span>
                }
            </app-button>
        }
        <app-button
            variant="plain"
            class="no-mt"
            (buttonClick)="switchAnalysisMode()"
        >
            @if (mode === 'analysis') {
                <mat-icon class="material-symbols-outlined"
                    >data_info_alert</mat-icon
                >
                <span>{{
                    'tender.edit-modal.analytics-mode-off-button' | translate
                }}</span>
            } @else {
                <mat-icon class="material-symbols-outlined">analytics</mat-icon>
                <span>{{
                    'tender.edit-modal.analytics-mode-on-button' | translate
                }}</span>
            }
        </app-button>
    </div>
    @if (mode === 'read') {
        <app-tender-details [tender]="tender ?? null"></app-tender-details>
    } @else if (mode === 'analysis') {
        <app-tender-analysis
            [tenderId]="tender?.id ?? ''"
        ></app-tender-analysis>
    } @else if (mode === 'edit') {
        <div class="tender-edit-user-info-container">
            <div
                class="inner-container info-grid gap-verticalPad pt-2 pb-2 mb-4"
            >
                <div>
                    <h4>
                        <span>
                            {{ 'tender.details.created-at.label' | translate }}:
                        </span>
                    </h4>
                </div>
                <div>
                    <h4>
                        <span class="ml-4"
                            >{{ tender?.creationTime | localeDate: 'short' }}
                        </span>
                    </h4>
                </div>
                <div>
                    @if (tender?.createdBy?.fullName) {
                        <h4>
                            <span>({{ tender?.createdBy?.fullName }})</span>
                        </h4>
                    }
                </div>
                <div>
                    <h4>
                        <span>
                            {{
                                'tender.details.modified-at.label' | translate
                            }}:
                        </span>
                    </h4>
                </div>
                <div>
                    <h4>
                        <span class="ml-4"
                            >{{ tender?.lastUpdatedTime | localeDate: 'short' }}
                        </span>
                    </h4>
                </div>
                <div>
                    @if (tender?.lastUpdatedBy?.fullName) {
                        <h4>
                            <span>({{ tender?.lastUpdatedBy?.fullName }})</span>
                        </h4>
                    }
                </div>
            </div>
        </div>
        <app-separator separatorType="horizontal"></app-separator>
        <app-tender-update [tender]="tender"></app-tender-update>
    }
    @if (loading$.getValue()) {
        <div class="loading-overlay">
            <mat-progress-spinner
                mode="indeterminate"
                diameter="50"
            ></mat-progress-spinner>
        </div>
    }
</app-modal>
