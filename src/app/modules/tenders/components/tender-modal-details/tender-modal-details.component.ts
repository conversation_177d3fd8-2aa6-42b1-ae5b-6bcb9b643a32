import { Component, inject, OnInit, output, ViewChild } from '@angular/core'
import { MatIcon } from '@angular/material/icon'
import { MatProgressSpinner } from '@angular/material/progress-spinner'

import { TenderDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'
import { TenderAnalysisComponent } from '@modules/tenders/components/tender-analysis/tender-analysis.component'
import { TenderDetailsComponent } from '@modules/tenders/components/tender-details/tender-details.component'
import { TenderUpdateComponent } from '@modules/tenders/components/tender-update/tender-update.component'
import { TenderService } from '@modules/tenders/services/tender.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import { ModalComponent } from '@shared/components/modal/modal.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { DialogService } from '@shared/services/dialog.service'
import { PopUpService } from '@shared/services/pop-up.service'
import { LocaleDatePipe } from '@shared/utils/custom-pipes/locale-date.pipe'
import { createURLVariables } from '@shared/utils/url-query-parameter-store/url-query-param-store'

import { TranslateModule, TranslateService } from '@ngx-translate/core'
import { BehaviorSubject, finalize } from 'rxjs'

@Component({
    selector: 'app-tender-modal-details',
    imports: [
        SeparatorComponent,
        ModalComponent,
        TranslateModule,
        MatProgressSpinner,
        TenderDetailsComponent,
        ButtonComponent,
        MatIcon,
        TenderAnalysisComponent,
        TenderUpdateComponent,
        LocaleDatePipe,
    ],
    templateUrl: './tender-modal-details.component.html',
    standalone: true,
    styleUrl: './tender-modal-details.component.scss',
})
export class TenderModalDetailsComponent implements OnInit {
    private readonly translate = inject(TranslateService)

    closed = output()

    queryParamStore = createURLVariables('selectedTender', 'modal')
    private get tenderId() {
        return this.queryParamStore.get().selectedTender
    }

    @ViewChild(TenderUpdateComponent)
    tenderUpdateComponent!: TenderUpdateComponent

    loading$ = new BehaviorSubject<boolean>(true)
    tender: TenderDTO | undefined
    mode: 'read' | 'edit' | 'analysis' = 'read'

    constructor(
        private tenderService: TenderService,
        private logger: LoggerService,
        private popUpService: PopUpService,
        private confirmDialogService: DialogService
    ) {}

    ngOnInit(): void {
        this.fetchTender()
    }

    private fetchTender(): void {
        if (!this.tenderId) {
            this.logger.warn('tenderId was undefined')
            this.onClose()
            return
        }

        this.loading$.next(true)

        this.tenderService
            .getTenderById(this.tenderId)
            .pipe(finalize(() => this.loading$.next(false)))
            .subscribe({
                next: (tender) => {
                    this.tender = tender
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.fetch-tender'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
    }

    async onCancel() {
        if (this.mode === 'edit' && this.tenderUpdateComponent?.isDirty()) {
            const confirm = await this.confirmDialogService.openConfirmDialog()
            if (confirm) {
                this.tenderUpdateComponent.resetForm(this.tender)
                this.mode = 'read'
            }
            return
        }

        this.mode = 'read'
    }

    onSave() {
        if (this.mode !== 'edit') {
            return
        }

        this.loading$.next(true)

        this.tenderUpdateComponent
            .onSave()
            .pipe(finalize(() => this.loading$.next(false)))
            .subscribe({
                next: (result: TenderDTO) => {
                    this.tender = result
                    const successMessage = this.translate.instant(
                        'popup.success.update-tender'
                    )
                    this.popUpService.openSuccessPopUp(successMessage)
                    this.mode = 'read'
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.update-tender'
                    )
                    this.popUpService.openErrorPopUp(errorMessage)
                },
            })
    }

    async switchAnalysisMode() {
        if (this.mode === 'edit' && this.tenderUpdateComponent?.isDirty()) {
            const confirm = await this.confirmDialogService.openConfirmDialog()
            if (!confirm) return
        }

        if (this.mode === 'analysis') this.mode = 'read'
        else this.mode = 'analysis'
    }

    async onClose() {
        if (this.mode === 'edit' && this.tenderUpdateComponent?.isDirty()) {
            const confirm = await this.confirmDialogService.openConfirmDialog()
            if (!confirm) return
        }

        this.closed.emit()
    }

    getHeader() {
        switch (this.mode) {
            case 'edit':
                return this.translate.instant('tender.edit-modal.title')
            case 'analysis':
                return this.translate.instant('tender.details.analysis.title')
            case 'read':
            default:
                return this.translate.instant('tender.details.title')
        }
    }
}
