<form
    [formGroup]="tenderDetailsForm"
    class="w-full flex flex-col gap-verticalPad"
>
    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.name-input.label' | translate"
                inputId="title"
                formControlName="title"
                [placeholder]="
                    'tender.details.name-input.placeholder' | translate
                "
            >
            </app-input-field-text>
        </div>
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.client.label' | translate"
                inputId="client"
                formControlName="client"
                [placeholder]="'tender.details.client.placeholder' | translate"
            ></app-input-field-text>
        </div>
    </div>

    <app-input-field-text
        [label]="'tender.details.sourceUrl.label' | translate"
        inputId="sourceUrl"
        formControlName="sourceUrl"
        [placeholder]="'tender.details.sourceUrl.placeholder' | translate"
        [hint]="'tender.details.sourceUrl.hint' | translate"
    ></app-input-field-text>

    <app-separator separatorType="horizontal"></app-separator>

    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-datetimepicker-field
                formControlName="publicationDate"
                [label]="'tender.details.publicationDate.label' | translate"
                format="date"
            ></app-datetimepicker-field>
        </div>
        <div class="flex-1">
            <app-datetimepicker-field
                formControlName="submissionDate"
                [label]="'tender.details.submissionDate.label' | translate"
                format="datetime"
                [hint]="'tender.details.submissionDate.hint' | translate"
            ></app-datetimepicker-field>
        </div>
    </div>
    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.contractDuration.label' | translate"
                inputId="contractDuration"
                formControlName="contractDuration"
                [placeholder]="
                    'tender.details.contractDuration.placeholder' | translate
                "
            ></app-input-field-text>
        </div>
        <div class="flex-1">
            <app-input-field-number
                [label]="'tender.details.contractValue.label' | translate"
                inputId="contractValue"
                formControlName="contractValue"
                [placeholder]="
                    'tender.details.contractValue.placeholder' | translate
                "
                [hint]="'tender.details.contractValue.hint' | translate"
                [inputFieldType]="'currency'"
            ></app-input-field-number>
        </div>
    </div>
    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.maximumBudget.label' | translate"
                inputId="maximumBudget"
                formControlName="maximumBudget"
                [placeholder]="
                    'tender.details.maximumBudget.placeholder' | translate
                "
                [hint]="'tender.details.maximumBudget.hint' | translate"
            ></app-input-field-text>
        </div>
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.deliveryLocation.label' | translate"
                inputId="deliveryLocation"
                formControlName="deliveryLocation"
                [placeholder]="
                    'tender.details.deliveryLocation.placeholder' | translate
                "
            ></app-input-field-text>
        </div>
    </div>
    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-input-field-text
                [label]="'tender.details.winningCriteria.label' | translate"
                inputId="winningCriteria"
                formControlName="winningCriteria"
                [placeholder]="
                    'tender.details.winningCriteria.placeholder' | translate
                "
                [hint]="'tender.details.winningCriteria.hint' | translate"
            ></app-input-field-text>
        </div>
        <div class="flex-1">
            <app-input-field-text
                [label]="
                    'tender.details.weightingPriceQuality.label' | translate
                "
                inputId="weightingPriceQuality"
                formControlName="weightingPriceQuality"
                [placeholder]="
                    'tender.details.weightingPriceQuality.placeholder'
                        | translate
                "
                [hint]="'tender.details.weightingPriceQuality.hint' | translate"
            ></app-input-field-text>
        </div>
    </div>
    <div class="flex gap-verticalPad">
        <div class="flex-1">
            <app-datetimepicker-field
                formControlName="questionDeadline"
                [label]="'tender.details.questionDeadline.label' | translate"
                format="datetime"
            ></app-datetimepicker-field>
        </div>
        <div class="flex-1">
            <app-datetimepicker-field
                formControlName="bindingDeadline"
                [label]="'tender.details.bindingDeadline.label' | translate"
                format="date"
                [hint]="'tender.details.bindingDeadline.hint' | translate"
            ></app-datetimepicker-field>
        </div>
    </div>

    <app-separator separatorType="horizontal"></app-separator>

    <app-input-textarea
        inputId="description"
        formControlName="description"
        [placeholder]="
            'tender.description.description-input.placeholder' | translate
        "
        [label]="'tender.description.description-input.label' | translate"
    >
    </app-input-textarea>

    <app-separator separatorType="horizontal"></app-separator>

    <div class="flex gap-verticalPad mb-2">
        <div class="flex-1">
            <app-input-textarea
                inputId="comment"
                formControlName="comment"
                [placeholder]="'tender.details.comment.placeholder' | translate"
                [label]="'tender.details.comment.label' | translate"
                [rows]="3"
            >
            </app-input-textarea>
        </div>
    </div>

    <app-separator separatorType="horizontal"></app-separator>

    <div
        class="flex flex-col md:flex-row items-start md:items-center gap-verticalPad md:gap-x-verticalPad"
    >
        <div class="flex items-center flex-1">
            <label for="ratingControl" class="mr-2">
                {{ 'tender.details.rating.label' | translate }}
            </label>
            <div class="rating flex items-center space-x-1">
                @for (i of [1, 2, 3, 4, 5]; track i) {
                    <button
                        type="button"
                        mat-icon-button
                        (click)="setRating(i)"
                    >
                        <mat-icon>
                            {{
                                (tenderDetailsForm.get('rating')?.value ?? 0) >=
                                i
                                    ? 'star'
                                    : 'star_border'
                            }}
                        </mat-icon>
                    </button>
                }
            </div>
        </div>

        <div class="flex flex-1 items-center space-x-4 mt-verticalPad md:mt-0">
            <mat-checkbox formControlName="isFavorite">
                {{ 'tender.details.isFavorite.label' | translate }}
            </mat-checkbox>
        </div>

        <div
            class="flex flex-1 items-center space-x-4 mt-verticalPad md:mt-0"
            *ngfor="let workflowStatus of WorkflowStatusDTO"
        >
            <span>{{ workflowStatus }}</span>
        </div>
    </div>
</form>
