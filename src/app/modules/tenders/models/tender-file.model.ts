import { FileMetadataDTO } from '@app/api'

export interface TenderFileItem {
    file: File
    tenderId?: string
    progress?: number
    selected?: boolean
    status?: FileStatus
}

export interface TenderMetadataFileItem {
    metadata: FileMetadataDTO
    tenderId: string
    progress?: number
    selected?: boolean
    status?: FileStatus
}

export interface TenderCombinedFileValue {
    newFiles: TenderFileItem[]
    metadataFiles: TenderMetadataFileItem[]
}

export enum FileStatus {
    Pending = 'pending',
    Uploading = 'uploading',
    Downloading = 'downloading',
    Deleting = 'deleting',
    SuccessUpload = 'successUpload',
    SuccessDownload = 'successDownload',
    ErrorDelete = 'errorDelete',
    ErrorUpload = 'errorUpload',
    ErrorDownload = 'errorDownload',
    New = 'new',
    Uploaded = 'uploaded',
    TooLarge = 'tooLarge',
}
