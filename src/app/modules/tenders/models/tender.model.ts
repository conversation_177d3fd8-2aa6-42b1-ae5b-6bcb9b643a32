import { FormControl } from '@angular/forms'

import { WorkflowStatusDTO } from '@app/api'
import { TenderCombinedFileValue } from '@modules/tenders/models/tender-file.model'

export interface TenderCreateFormControlModel {
    title: FormControl<string>
    sourceUrl: FormControl<string>
    client: FormControl<string>
    submissionDate: FormControl<string>
    bindingDeadline: FormControl<string>
    contractDuration: FormControl<string>
    publicationDate: FormControl<string>
    questionDeadline: FormControl<string>
    winningCriteria: FormControl<string>
    weightingPriceQuality: FormControl<string>
    deliveryLocation: FormControl<string>
    description: FormControl<string>
    contractValue: FormControl<number | null>
    maximumBudget: FormControl<string | null>
    comment?: FormControl<string | null>
    rating?: FormControl<number | null>
    isFavorite?: FormControl<boolean>
    workflowStatus: FormControl<WorkflowStatusDTO>
    files?: FormControl<TenderCombinedFileValue | null>
}

export interface TenderFileFormControlModel {
    files: FormControl<TenderCombinedFileValue | null>
}

export interface TenderCreateFormModel {
    title: string
    sourceUrl?: string
    client: string
    submissionDate: string
    bindingDeadline: string
    contractDuration: string
    publicationDate: string
    questionDeadline: string
    winningCriteria: string
    weightingPriceQuality: string
    deliveryLocation: string
    description: string
    contractValue: number | null
    maximumBudget: string | null
    comment?: string | null
    rating?: number | null
    isFavorite?: boolean
    workflowStatus: WorkflowStatusDTO
    files?: TenderCombinedFileValue | null | undefined
}

export interface TenderFileFormModel {
    files?: TenderCombinedFileValue | null | undefined
}

export interface TenderFile {
    filename: string
    size: number
    uploadDateTime: string
    format: string
}
