<app-page-layout>
    <div upper>
        <h2>
            {{ 'all-tenders-component.all-tenders-header' | translate }}
        </h2>

        <div class="flex">
            <!-- search -->
            <app-input-field-text
                #filterInputField
                inputId="search"
                class="w-full pr-4"
                [placeholder]="
                    'all-tenders-component.input-filter-placeholder' | translate
                "
                icon="search"
                (valueChange)="onSearchChange($event)"
                [disabled]="true"
            ></app-input-field-text>
            <app-button
                class="app-button-right"
                icon="add"
                (buttonClick)="onAddTender()"
            >
                {{ 'all-tenders-component.add-button' | translate }}</app-button
            >
        </div>
    </div>

    <div lower>
        <app-all-tenders-table class="w-full center-if-desktop mt-4">
        </app-all-tenders-table>
    </div>
</app-page-layout>

@if (queryParamStore.get().modal === 'add') {
    <app-tender-modal-create
        (closed)="onModalClosed()"
    ></app-tender-modal-create>
}
