import {
    AfterViewInit,
    Component,
    effect,
    ViewChild,
    viewChild,
} from '@angular/core'

import { AllTendersTableComponent } from '@app/modules/tenders/components/all-tenders-table/all-tenders-table.component'
import { TenderModalCreateComponent } from '@app/modules/tenders/components/tender-modal-create/tender-modal-create.component'
import { InputFieldTextComponent } from '@app/shared/components/input-field-text/input-field-text.component'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { ButtonComponent } from '@shared/components/button/button.component'
import { PageLayoutComponent } from '@shared/components/layout/page-layout/page-layout.component'

import { TranslateModule } from '@ngx-translate/core'

export type AllTendersModalTypes = 'add' | 'edit' | undefined

@Component({
    selector: 'app-all-tenders',
    standalone: true,
    imports: [
        ButtonComponent,
        InputFieldTextComponent,
        AllTendersTableComponent,
        TenderModalCreateComponent,
        TranslateModule,
        PageLayoutComponent,
    ],
    templateUrl: './all-tenders.component.html',
    styleUrl: './all-tenders.component.scss',
})
export class AllTendersComponent implements AfterViewInit {
    @ViewChild(AllTendersTableComponent) table!: AllTendersTableComponent
    filterInputField = viewChild<InputFieldTextComponent>('filterInputField')
    queryParamStore = createURLVariables('modal', 'filter')

    constructor() {
        effect(() => {
            this.table?.changeFilter(this.queryParamStore.get().filter ?? '')
            this.filterInputField()?.writeValue(
                this.queryParamStore.get().filter ?? ''
            )
        })
    }

    ngAfterViewInit(): void {
        this.table?.changeFilter(this.queryParamStore.get().filter ?? '')
    }

    onSearchChange(value: string) {
        const filter = value.trim().toLowerCase()
        if (filter == '') this.updateFilter(undefined)
        else this.updateFilter(filter)
    }

    onAddTender() {
        this.updateModal('add')
    }

    onModalClosed() {
        this.updateModal(undefined)
        this.table.fetchTenders(true)
    }

    private updateFilter(filter: string | undefined) {
        this.queryParamStore.update((vars) => {
            vars.filter = filter
            return vars
        })
    }

    private updateModal(modal: AllTendersModalTypes) {
        this.queryParamStore.update((vars) => {
            vars.modal = modal
            return vars
        })
    }
}
