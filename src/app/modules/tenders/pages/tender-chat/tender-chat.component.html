<app-page-layout>
    <div upper>
        <h2>Modify <PERSON></h2>
        <div class="section-selection flex flex-row justify-start mt-4">
            <a
                tabindex="0"
                (keydown.enter)="switchSection('details')"
                (click)="switchSection('details')"
                [ngClass]="{
                    'link-active':
                        queryParamStore.get().selectedSection === 'details' ||
                        queryParamStore.get().selectedSection === undefined,
                }"
            >
                Details
            </a>
            <a
                tabindex="0"
                (keydown.enter)="switchSection('description')"
                (click)="switchSection('description')"
                [ngClass]="{
                    'link-active':
                        queryParamStore.get().selectedSection === 'description',
                }"
            >
                Description
            </a>
            <a
                tabindex="0"
                (keydown.enter)="switchSection('documents')"
                (click)="switchSection('documents')"
                [ngClass]="{
                    'link-active':
                        queryParamStore.get().selectedSection === 'documents',
                }"
            >
                Documents
            </a>
            <a
                tabindex="0"
                (keydown.enter)="switchSection('ai-chat')"
                (click)="switchSection('ai-chat')"
                [ngClass]="{
                    'link-active':
                        queryParamStore.get().selectedSection === 'ai-chat',
                }"
            >
                Ai-chat
            </a>
        </div>
    </div>

    <div lower>
        <div class="py-verticalPad h-full">
            @switch (queryParamStore.get().selectedSection) {
                @case ('description') {
                    <app-tender-description
                        (updatedTender)="fetchTender()"
                        [tenderId]="tender?.id ?? null"
                        class="h-full block"
                    ></app-tender-description>
                }
                @case ('documents') {
                    <app-tender-documents
                        (updateTender)="fetchTender()"
                        [tender]="tender"
                    ></app-tender-documents>
                }
                @case ('ai-chat') {
                    <div><h2>ai-chat</h2></div>
                }
            }
        </div>
    </div>
</app-page-layout>
