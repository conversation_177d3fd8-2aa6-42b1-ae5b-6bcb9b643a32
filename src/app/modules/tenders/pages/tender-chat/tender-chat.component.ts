import { CommonModule } from '@angular/common'
import { Component, computed, inject, OnInit, viewChild } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'

import { TenderDTO } from '@app/api'
import { LoggerService } from '@app/core/logging/logger.service'
import { CanDeactivateType } from '@app/shared/can-deactivate-guard'
import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '@app/shared/components/confirm-dialog/confirm-dialog.component'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { TenderDescriptionComponent } from '@modules/tenders/components/tender-description/tender-description.component'
import { PageLayoutComponent } from '@shared/components/layout/page-layout/page-layout.component'
import { PopUpService } from '@shared/services/pop-up.service'

import { TenderDocumentsComponent } from '../../components/tender-documents/tender-documents.component'
import { TenderService } from '../../services/tender.service'

import { TranslateService } from '@ngx-translate/core'
import { firstValueFrom } from 'rxjs'
type Sections = 'description' | 'documents' | 'ai-chat' | 'details'

@Component({
    selector: 'app-tender-chat',
    imports: [
        CommonModule,
        TenderDocumentsComponent,
        PageLayoutComponent,
        TenderDescriptionComponent,
    ],
    templateUrl: './tender-chat.component.html',
    styleUrl: './tender-chat.component.scss',
    standalone: true,
})
export class TenderChatComponent implements OnInit {
    private readonly translate = inject(TranslateService)

    tenderDescriptionComponent = viewChild<TenderDescriptionComponent>(
        TenderDescriptionComponent
    )

    tenderId!: string
    tender: TenderDTO | undefined
    queryParamStore = createURLVariables('selectedSection')
    readonly dialog = inject(MatDialog)

    isDirty = computed(() => {
        return this.tenderDescriptionComponent()?.isDirty()
    })

    constructor(
        private tendersService: TenderService,
        private logger: LoggerService,
        private route: ActivatedRoute,
        private popUpService: PopUpService
    ) {}

    ngOnInit(): void {
        const tenderIdFromRoute = this.route.snapshot.parent?.data['tenderId']
        if (tenderIdFromRoute == undefined) {
            this.logger.warn('tenderId was not defined')
            return
        }
        this.tenderId = tenderIdFromRoute

        this.fetchTender()
    }

    fetchTender() {
        this.tendersService.getTenderById(this.tenderId).subscribe({
            next: (tender) => {
                this.tender = tender
            },
            error: () => {
                const errorMessage = this.translate.instant(
                    'popup.error.fetch-tender'
                )
                this.popUpService.openErrorPopUp(errorMessage)
            },
        })
    }

    // For can-deactivate-guard
    async canDeactivate(): Promise<CanDeactivateType> {
        if (this.isDirty()) {
            return await this.confirmDiscardChanges()
        }
        return true // Allow navigation if there are no unsaved changes
    }

    async switchSection(section: Sections) {
        if (this.isDirty() && !(await this.confirmDiscardChanges())) return

        this.queryParamStore.update((store) => {
            store.selectedSection = section
        })
    }

    async confirmDiscardChanges() {
        const header = this.translate.instant(
            'confirm-dialog.unsaved-changes.header'
        )
        const text = this.translate.instant(
            'confirm-dialog.unsaved-changes.text'
        )

        const dialogData: ConfirmDialogData = {
            header: header,
            text: text,
        }

        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            data: dialogData,
        })

        return await firstValueFrom(dialogRef.afterClosed())
    }
}
