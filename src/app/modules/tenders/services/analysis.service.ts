import { Injectable } from '@angular/core'

import { AnalysisResultDTO } from '@app/api'
import { AnalysisApiService } from '@app/api/api/analysis.service'
import { ApiErrorHandlerService } from '@shared/services/api-error-handler.service'

import { Observable } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class AnalysisService {
    constructor(
        private analysisService: AnalysisApiService,
        private apiErrorHandler: ApiErrorHandlerService
    ) {}

    analyzeTender(tenderId: string): Observable<AnalysisResultDTO> {
        return this.analysisService
            .analyzeTender(tenderId)
            .pipe(
                this.apiErrorHandler.handleError<AnalysisResultDTO>(
                    'analyzeTender'
                )
            )
    }
}
