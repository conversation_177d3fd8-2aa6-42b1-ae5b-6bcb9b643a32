import { HttpEvent } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { FileMetadataDTO } from '@app/api'
import { TenderApiService } from '@app/api/api/tender.service'
import { ApiErrorHandlerService } from '@shared/services/api-error-handler.service'

import { Observable } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class TenderFileService {
    constructor(
        private tenderService: TenderApiService,
        private apiErrorHandler: ApiErrorHandlerService
    ) {}

    addTenderFile(
        tenderId: string,
        file: Blob
    ): Observable<HttpEvent<FileMetadataDTO>> {
        return this.tenderService
            .addTenderFile(tenderId, file, 'events', true)
            .pipe(
                this.apiErrorHandler.handleError<HttpEvent<FileMetadataDTO>>(
                    'addTenderFile'
                )
            )
    }

    deleteTenderFile(
        tenderId: string,
        fileId: string
    ): Observable<HttpEvent<any>> {
        return this.tenderService
            .deleteTenderFile(tenderId, fileId, 'events', true)
            .pipe(
                this.apiErrorHandler.handleError<HttpEvent<any>>(
                    'deleteTenderFile'
                )
            )
    }

    downloadTenderFile(
        tenderId: string,
        fileId: string
    ): Observable<HttpEvent<Blob>> {
        return this.tenderService
            .downloadTenderFile(tenderId, fileId, 'events', true)
            .pipe(
                this.apiErrorHandler.handleError<HttpEvent<Blob>>(
                    'downloadTenderFile'
                )
            )
    }
}
