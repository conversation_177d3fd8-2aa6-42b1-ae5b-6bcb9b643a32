<div class="relative" appClickOutside (clickOutside)="closeMenu()">
    <app-button
        buttonId="open-menu"
        [ariaLabel]="'menu-modal.open-button.aria-label' | translate"
        variant="avatar"
        [class]="'no-mt'"
        (buttonClick)="toggleMenu()"
    >
        <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="40" height="40" rx="20" fill="currentColor" />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M26.0002 16C26.0002 19.3137 23.314 22 20.0002 22C16.6865 22 14.0002 19.3137 14.0002 16C14.0002 12.6863 16.6865 10 20.0002 10C23.314 10 26.0002 12.6863 26.0002 16ZM24.0002 16C24.0002 18.2091 22.2094 20 20.0002 20C17.7911 20 16.0002 18.2091 16.0002 16C16.0002 13.7909 17.7911 12 20.0002 12C22.2094 12 24.0002 13.7909 24.0002 16Z"
                fill="#4F378A"
            />
            <path
                d="M20.0002 25C13.5259 25 8.00952 28.8284 5.9082 34.192C6.4201 34.7004 6.95934 35.1812 7.52353 35.6321C9.08827 30.7077 13.997 27 20.0002 27C26.0035 27 30.9122 30.7077 32.477 35.6321C33.0412 35.1812 33.5804 34.7004 34.0923 34.1921C31.991 28.8284 26.4746 25 20.0002 25Z"
                fill="#4F378A"
            />
        </svg>
    </app-button>
    @if (menuOpen) {
        <div class="user-menu-wrapper">
            <div class="user-menu-container">
                <div class="profile-section p-verticalPad">
                    <div>
                        <svg
                            width="30"
                            height="30"
                            viewBox="0 0 30 30"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M21.3866 7.02245C21.3866 10.9008 18.5272 14.0449 15 14.0449C11.4728 14.0449 8.61341 10.9008 8.61341 7.02245C8.61341 3.14406 11.4728 0 15 0C18.5272 0 21.3866 3.14406 21.3866 7.02245ZM19.2577 7.02245C19.2577 9.60805 17.3515 11.7041 15 11.7041C12.6485 11.7041 10.7423 9.60805 10.7423 7.02245C10.7423 4.43686 12.6485 2.34082 15 2.34082C17.3515 2.34082 19.2577 4.43686 19.2577 7.02245Z"
                                fill="#0f3e52"
                            />
                            <path
                                d="M15 17.5561C8.10847 17.5561 2.2367 22.0369 0 28.3146C0.544877 28.9095 1.11886 29.4722 1.7194 30C3.38496 24.2365 8.60992 19.8969 15 19.8969C21.3901 19.8969 26.615 24.2365 28.2806 30C28.8811 29.4723 29.4551 28.9095 30 28.3146C27.7633 22.0369 21.8915 17.5561 15 17.5561Z"
                                fill="#0f3e52"
                            />
                        </svg>
                    </div>
                    <div>
                        <h4 class="name">
                            {{ getUserDetails()?.name }}
                        </h4>
                        <p class="email">
                            {{ getUserDetails()?.email }}
                        </p>
                    </div>
                </div>

                <app-separator
                    separatorType="horizontal"
                    class="w-full"
                ></app-separator>

                <div class="p-verticalPad w-full">
                    <a
                        class="cursor-pointer w-full block hover:text-primary"
                        [routerLink]="SYSTEM_CONFIGURATION_URL"
                        tabindex="0"
                        >{{ 'tender.config.label' | translate }}</a
                    >
                </div>

                <app-separator
                    separatorType="horizontal"
                    class="w-full"
                ></app-separator>

                <!-- switch language -->
                <div class="p-verticalPad w-full flex">
                    <fieldset
                        class="language-picker flex flex-row w-full items-center"
                    >
                        <legend class="title">
                            <i class="icon mr-1" role="img"
                                ><svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 256 256"
                                    fill="currentColor"
                                >
                                    <path
                                        d="M239.15,212.42l-56-112a8,8,0,0,0-14.31,0l-21.71,43.43A88,88,0,0,1,100,126.93,103.65,103.65,0,0,0,127.69,64H152a8,8,0,0,0,0-16H96V32a8,8,0,0,0-16,0V48H24a8,8,0,0,0,0,16h87.63A87.76,87.76,0,0,1,88,116.35a87.74,87.74,0,0,1-19-31,8,8,0,1,0-15.08,5.34A103.63,103.63,0,0,0,76,127a87.55,87.55,0,0,1-52,17,8,8,0,0,0,0,16,103.46,103.46,0,0,0,64-22.08,104.18,104.18,0,0,0,51.44,21.31l-26.6,53.19a8,8,0,0,0,14.31,7.16L140.94,192h70.11l13.79,27.58A8,8,0,0,0,232,224a8,8,0,0,0,7.15-11.58ZM148.94,176,176,121.89,203.05,176Z"
                                    ></path></svg></i
                            ><span class="prefix">{{
                                'menu-modal.switch-language-title' | translate
                            }}</span>
                        </legend>
                        <!-- English -->
                        <label
                            for="language-picker-en"
                            class="label"
                            [class.active]="currentLang === 'en'"
                        >
                            <input
                                id="language-picker-en"
                                type="radio"
                                name="language"
                                value="en"
                                [checked]="currentLang === 'en'"
                                (change)="onLanguageChange('en')"
                            />
                            EN
                        </label>

                        <!-- German -->
                        <label
                            for="language-picker-de"
                            class="label"
                            [class.active]="currentLang === 'de'"
                        >
                            <input
                                id="language-picker-de"
                                type="radio"
                                name="language"
                                value="de"
                                [checked]="currentLang === 'de'"
                                (change)="onLanguageChange('de')"
                            />
                            DE
                        </label>

                        <!-- Finnish -->
                        <label
                            for="language-picker-fi"
                            class="label"
                            [class.active]="currentLang === 'fi'"
                        >
                            <input
                                id="language-picker-fi"
                                type="radio"
                                name="language"
                                value="fi"
                                [checked]="currentLang === 'fi'"
                                (change)="onLanguageChange('fi')"
                            />
                            FI
                        </label>
                    </fieldset>
                </div>

                <app-separator
                    separatorType="horizontal"
                    class="w-full"
                ></app-separator>

                <div class="p-verticalPad w-full">
                    <app-button (buttonClick)="logout()" variant="secondary">{{
                        'menu-modal.log-out-button' | translate
                    }}</app-button>
                </div>
            </div>
        </div>
    }
</div>

@if (queryParamStore.get().modal === 'prompt-configuration') {
    <app-prompt-configuration></app-prompt-configuration>
}
