import { Component } from '@angular/core'
import { RouterLink } from '@angular/router'

import { AuthService } from '@app/core/auth/auth.service'
import { ClickOutsideDirective } from '@app/shared/directives/click-outside.directive'
import { createURLVariables } from '@app/shared/utils/url-query-parameter-store/url-query-param-store'
import { SYSTEM_CONFIGURATION_URL } from '@core/auth/auth.constants'
import { SystemConfigurationComponent } from '@modules/user-menu/pages/system-configuration/system-configuration.component'
import { ButtonComponent } from '@shared/components/button/button.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { AvailableLanguages, I18nService } from '@shared/services/i18n.service'

import { TranslateModule } from '@ngx-translate/core'

@Component({
    selector: 'app-user-profile-dropdown',
    imports: [
        ButtonComponent,
        SeparatorComponent,
        SystemConfigurationComponent,
        ClickOutsideDirective,
        TranslateModule,
        RouterLink,
    ],
    templateUrl: './menu-modal.component.html',
    standalone: true,
    styleUrl: './menu-modal.component.scss',
})
export class UserProfileDropdownComponent {
    menuOpen = false
    queryParamStore = createURLVariables('modal')
    currentLang: string

    constructor(
        private authService: AuthService,
        private translationService: I18nService
    ) {
        this.currentLang = this.translationService.getCurrentLanguage()
    }

    toggleMenu() {
        this.menuOpen = !this.menuOpen
    }

    closeMenu() {
        this.menuOpen = false
    }

    getUserDetails() {
        return this.authService.getUserDetails()
    }

    logout() {
        void this.authService.signOut()
    }

    onLanguageChange(lang: AvailableLanguages) {
        this.translationService.setLanguage(lang)
        this.currentLang = lang
    }

    protected readonly SYSTEM_CONFIGURATION_URL = SYSTEM_CONFIGURATION_URL
}
