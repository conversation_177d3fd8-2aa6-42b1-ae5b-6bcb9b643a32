<app-page-layout>
    <div upper>
        <!-- Tab headers only in the upper section -->
        <mat-tab-group
            [(selectedIndex)]="selectedIndex"
            class="system-configuration-tabs-header"
        >
            <mat-tab
                label="{{
                    'system-configuration.prompts.system-prompt-section.header'
                        | translate
                }}"
            ></mat-tab>
            <mat-tab
                label="{{
                    'system-configuration.prompts.analysis-prompt-section.header'
                        | translate
                }}"
            ></mat-tab>
            <mat-tab
                label="{{
                    'system-configuration.prompts.structured-output-prompt-section.header'
                        | translate
                }}"
            ></mat-tab>
        </mat-tab-group>
    </div>
    <div lower>
        @if (loading$.getValue()) {
            <div class="loading-overlay">
                <mat-progress-spinner
                    mode="indeterminate"
                    diameter="50"
                ></mat-progress-spinner>
            </div>
        } @else {
            <form
                [formGroup]="configForm"
                (submit)="updateSystemConfig()"
                class="content flex flex-col h-full gap-verticalGap"
            >
                <!-- Content only - no tab headers -->
                <div class="tab-content flex-1">
                    @switch (selectedIndex) {
                        @case (0) {
                            <app-md-editor
                                [control]="systemPromptControl"
                                [original]="
                                    systemConfig?.aiAnalysisSystemPrompt || ''
                                "
                                class="flex w-full flex-1 flex-shrink-0"
                            ></app-md-editor>
                        }
                        @case (1) {
                            <app-md-editor
                                [control]="analysisPromptControl"
                                [original]="
                                    systemConfig?.aiAnalysisAnalysisPrompt || ''
                                "
                                class="flex w-full flex-1 flex-shrink-0"
                            ></app-md-editor>
                        }
                        @case (2) {
                            <app-md-editor
                                [control]="structuredOutputPromptControl"
                                [original]="
                                    systemConfig?.aiStructuredOutputPrompt || ''
                                "
                                class="flex w-full flex-1 flex-shrink-0"
                            ></app-md-editor>
                        }
                    }
                </div>
                <div class="flex justify-end space-x-2 mt-6">
                    <app-button
                        (buttonClick)="onCancel()"
                        icon="close"
                        variant="secondary"
                        [disabled]="!isDirty()"
                        >{{ 'modal.cancel-button' | translate }}</app-button
                    >
                    <app-button
                        [type]="'submit'"
                        [disabled]="!isDirty()"
                        tabindex="0"
                        icon="save"
                        >{{ 'modal.save-button' | translate }}</app-button
                    >
                </div>
            </form>
        }
    </div>
</app-page-layout>
