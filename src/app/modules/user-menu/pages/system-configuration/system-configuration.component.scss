.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.tabs-header-only .mat-mdc-tab-body-wrapper {
    display: none;
}
.tab-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}
.tab-content app-md-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.tab-content app-md-editor .md-editor {
    flex: 1;
    height: 100%;
}
