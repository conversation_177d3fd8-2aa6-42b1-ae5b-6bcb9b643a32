import { Component, inject, OnInit, signal } from '@angular/core'
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { MatProgressSpinner } from '@angular/material/progress-spinner'
import { MatTab, MatTabGroup } from '@angular/material/tabs'

import { SystemConfigurationDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import { PageLayoutComponent } from '@shared/components/layout/page-layout/page-layout.component'
import { MdEditorComponent } from '@shared/components/md-editor/md-editor.component'
import { DialogService } from '@shared/services/dialog.service'
import { PopUpService } from '@shared/services/pop-up.service'
import { SystemConfigurationService } from '@shared/services/system-configuration.service'
import { createURLVariables } from '@shared/utils/url-query-parameter-store/url-query-param-store'

import { TranslateModule, TranslateService } from '@ngx-translate/core'
import { BehaviorSubject, finalize } from 'rxjs'

@Component({
    selector: 'app-prompt-configuration',
    imports: [
        ReactiveFormsModule,
        TranslateModule,
        ButtonComponent,
        PageLayoutComponent,
        MatProgressSpinner,
        MatTab,
        MatTabGroup,
        MdEditorComponent,
    ],
    templateUrl: './system-configuration.component.html',
    styleUrl: './system-configuration.component.scss',
    standalone: true,
})
export class SystemConfigurationComponent implements OnInit {
    private readonly translate = inject(TranslateService)
    queryParamStore = createURLVariables('modal')
    selectedIndex = 0

    protected configForm = new FormGroup({
        aiAnalysisSystemPrompt: new FormControl(''),
        aiAnalysisAnalysisPrompt: new FormControl(''),
        aiStructuredOutputPrompt: new FormControl(''),
    })

    protected formValid = signal<boolean>(false)
    public isDirty = signal<boolean>(false)

    loading$ = new BehaviorSubject<boolean>(true)

    systemConfig: SystemConfigurationDTO | null | undefined

    get systemPromptControl(): FormControl<string> {
        return this.configForm.get(
            'aiAnalysisSystemPrompt'
        ) as FormControl<string>
    }

    get analysisPromptControl(): FormControl<string> {
        return this.configForm.get(
            'aiAnalysisAnalysisPrompt'
        ) as FormControl<string>
    }

    get structuredOutputPromptControl(): FormControl<string> {
        return this.configForm.get(
            'aiStructuredOutputPrompt'
        ) as FormControl<string>
    }

    constructor(
        private systemConfigurationService: SystemConfigurationService,
        private logger: LoggerService,
        private popUpService: PopUpService,
        private confirmDialogService: DialogService
    ) {
        this.configForm.valueChanges.subscribe(() => {
            this.isDirty.set(this.configForm.dirty)
            this.formValid.set(this.configForm.valid)
        })
    }

    ngOnInit(): void {
        this.fetchSystemConfig()
    }

    private fetchSystemConfig(): void {
        this.loading$.next(true)

        this.systemConfigurationService
            .getSystemConfig()
            .pipe(finalize(() => this.loading$.next(false)))
            .subscribe({
                next: (result) => {
                    this.systemConfig = result
                    this.configForm.reset(result)
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.fetch-system-config'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
    }

    updateSystemConfig(): void {
        this.loading$.next(true)

        const updatedConfig: SystemConfigurationDTO = {
            aiAnalysisAnalysisPrompt:
                this.configForm.value.aiAnalysisAnalysisPrompt ??
                this.systemConfig!.aiAnalysisAnalysisPrompt,
            aiAnalysisSystemPrompt:
                this.configForm.value.aiAnalysisSystemPrompt ??
                this.systemConfig!.aiAnalysisSystemPrompt,
            aiStructuredOutputPrompt:
                this.configForm.value.aiStructuredOutputPrompt ??
                this.systemConfig!.aiStructuredOutputPrompt,
        }

        this.systemConfigurationService
            .updateSystemConfig(updatedConfig)
            .pipe(finalize(() => this.loading$.next(false)))
            .subscribe({
                next: (result) => {
                    this.systemConfig = result
                    this.configForm.reset(result)

                    const successMessage = this.translate.instant(
                        'popup.success.update-system-config'
                    )
                    this.popUpService.openSuccessPopUp(
                        this.translate.instant(successMessage)
                    )
                },
                error: (err) => {
                    this.logger.warn(err)
                    const errorMessage = this.translate.instant(
                        'popup.error.update-system-config'
                    )
                    this.popUpService.openErrorPopUp(
                        this.translate.instant(errorMessage)
                    )
                },
            })
    }

    async onCancel() {
        if (this.configForm.dirty)
            if (!(await this.confirmDialogService.openConfirmDialog())) return

        this.configForm.reset(this.systemConfig!)
    }
}
