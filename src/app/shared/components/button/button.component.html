<button
    [type]="type()"
    [ngClass]="[class() + ' ' + variant() + ' app-button ' + size()]"
    [disabled]="disabled()"
    [id]="buttonId()"
    [attr.aria-label]="ariaLabel()"
    (click)="handleClick($event)"
    (keyup.enter)="handleClick($event)"
    tabindex="0"
>
    @if (icon()) {
        <span class="material-symbols-outlined icon">
            {{ icon() }}
        </span>
    }
    <ng-content></ng-content>
</button>
