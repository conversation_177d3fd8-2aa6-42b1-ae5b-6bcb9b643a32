.app-button:not(.round) {
    min-width: fit-content;
}

.pointer-disabled {
    pointer-events: none;
    cursor: default;
}

button {
    width: 100%;
    text-align: center;
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 16px;
    line-height: normal;
    font-family: Cadiz-Regular, Arial, 'Helvetica Neue', sans-serif;
    font-feature-settings: 'liga' off;
    font-style: normal;
    max-height: 46px;

    &:not(.no-mt) {
        margin-top: var(--mat-form-field-container-vertical-padding, 16px);
    }

    &:not(.hover-disabled):hover {
        background-color: var(--primary-highlighted-color);
    }

    .icon {
        margin-left: -6px;
        font-size: 24px;
        cursor: pointer;
        user-select: none;
    }

    &[disabled] {
        background-color: var(--disabled) !important;
        pointer-events: none;
        cursor: default;
        color: var(--white);
        border: none;
    }
}

.small {
    padding: 3px 10px;
}
.medium {
    padding: 10px 17px;
}
.large {
    padding: 13px 20px;
}

.secondary {
    background-color: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);

    &:not(.hover-disabled):hover {
        background-color: var(--primary-highlighted-color-lighter);
    }

    &[disabled] {
        background-color: var(--white) !important;
        pointer-events: none;
        cursor: default;
        color: var(--disabled);
        border: 1px solid var(--disabled);
    }
}

.round {
    justify-content: center;
    align-items: center;
    border-radius: 10rem;
    background: var(--surface-third);
    height: 46px;
    width: 46px;
    display: inline-flex;
    color: var(--black);

    .icon {
        width: 0.875rem;
        height: 0.875rem;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 0;
    }
}

.avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100px;
    padding: 0;
    margin-top: unset;

    &:not(.hover-disabled):hover {
        color: var(--primary-highlighted-color);
    }

    svg {
        width: 100%;
        height: 100%;
        display: block;
    }
}

.toggle {
    color: var(--primary-highlighted-color);
    background-color: var(--color-secondary-Silver-Planet, #f0f5f5);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;

    &:not(.hover-disabled):hover {
        color: var(--white);
    }
}

.plain {
    font-family: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    color: var(--primary-color);
    background-color: transparent;
    padding: 0;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    margin: 0;
    width: fit-content;

    &:not(.hover-disabled):hover {
        border-radius: 100px;
        background-color: var(--primary-highlighted-color-lighter);
    }

    &[disabled] {
        background-color: transparent !important;
        pointer-events: none;
        cursor: default;
        color: var(--disabled);
        border: none;
    }
}
