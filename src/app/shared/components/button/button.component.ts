import { NgClass } from '@angular/common'
import { Component, input, output } from '@angular/core'

type StyleVariants =
    | 'primary'
    | 'secondary'
    | 'round'
    | 'avatar'
    | 'toggle'
    | 'plain'
type SizeVariants = 'small' | 'medium' | 'large'
type Types = 'button' | 'submit'

@Component({
    selector: 'app-button',
    standalone: true,
    imports: [NgClass],
    templateUrl: './button.component.html',
    styleUrl: './button.component.scss',
})
export class ButtonComponent {
    ariaLabel = input<string | undefined>(undefined)
    buttonId = input<string | undefined>(undefined)
    icon = input<string>('')
    variant = input<StyleVariants>('primary')
    type = input<Types>('button')
    disabled = input<boolean>(false)
    class = input<string>('')
    size = input<SizeVariants>('medium')
    buttonClick = output<MouseEvent>()

    handleClick(event: MouseEvent | any) {
        this.buttonClick.emit(event)
    }
}
