import { Component, inject } from '@angular/core'
import { MatButtonModule } from '@angular/material/button'
import {
    MAT_DIALOG_DATA,
    MatDialogActions,
    MatDialogContent,
    MatDialogRef,
    MatDialogTitle,
} from '@angular/material/dialog'
import { MatFormFieldModule } from '@angular/material/form-field'

import { ButtonComponent } from '@shared/components/button/button.component'

import { TranslatePipe, TranslateService } from '@ngx-translate/core'

export interface ConfirmDialogData {
    header?: string
    text?: string
}

@Component({
    selector: 'app-confirm-dialog',
    imports: [
        MatFormFieldModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        ButtonComponent,
        TranslatePipe,
    ],
    templateUrl: './confirm-dialog.component.html',
    styleUrl: './confirm-dialog.component.scss',
    standalone: true,
})
export class ConfirmDialogComponent {
    readonly dialogRef = inject(MatDialogRef<ConfirmDialogComponent>)
    readonly data = inject<ConfirmDialogData>(MAT_DIALOG_DATA)
    private readonly translate = inject(TranslateService)

    constructor() {
        if (this.data == null) {
            const header = this.translate.instant(
                'confirm-dialog.default.header'
            )
            const text = this.translate.instant('confirm-dialog.default.text')
            this.data = {
                header: header,
                text: text,
            }
        }

        // return false if clicked outside
        this.dialogRef.disableClose = true
        this.dialogRef.backdropClick().subscribe(() => {
            this.dialogRef.close(false)
        })
    }

    onNoClick(): void {
        this.dialogRef.close(false)
    }

    onYesClick() {
        this.dialogRef.close(true)
    }
}
