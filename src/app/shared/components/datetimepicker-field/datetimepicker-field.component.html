<div class="datetimepicker-field w-full h-full">
    <div class="flex mt-2 mb-1">
        <label class="mr-textIcon" [for]="datepickerId()">{{ label() }} </label>
        @if (requiredAsterisk) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Required icon"
                fontIcon="emergency"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'x-small' }"
            ></mat-icon>
        }
        @if (hint()) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Information icon"
                fontIcon="help_outline"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'medium' }"
            ></mat-icon>
        }
    </div>

    <!-- Date Only Mode -->
    @if (format() === 'date') {
        <ng-container>
            <mat-form-field
                [floatLabel]="'auto'"
                appearance="outline"
                class="date-field-custom-mat-field"
            >
                <input
                    matInput
                    [id]="datepickerId()"
                    [placeholder]="
                        'datetimepicker.date.placeholder' | translate
                    "
                    [matDatepicker]="dp"
                    [value]="value"
                    (dateChange)="onDateChange($event)"
                    (focusout)="onFocusOut()"
                    [errorStateMatcher]="errorMatcher"
                />
                <mat-datepicker-toggle
                    matSuffix
                    [for]="dp"
                ></mat-datepicker-toggle>
                <mat-datepicker color="primary" #dp></mat-datepicker>
                <mat-error>{{ errorMessage }}</mat-error>
            </mat-form-field>
        </ng-container>
    } @else {
        <!-- Date And Time Mode -->
        <ng-container>
            <mat-form-field
                [floatLabel]="'auto'"
                appearance="outline"
                class="datetime-date-field-custom-mat-field"
            >
                <div class="flex mt-2">
                    <input
                        matInput
                        [matDatepicker]="datepicker"
                        [value]="dateValue"
                        [placeholder]="
                            'datetimepicker.date.placeholder' | translate
                        "
                        (dateChange)="onDateChange($event)"
                        (focusout)="onFocusOut()"
                        [errorStateMatcher]="errorMatcher"
                    />
                    <mat-datepicker-toggle
                        [for]="datepicker"
                    ></mat-datepicker-toggle>
                </div>
                <mat-datepicker color="primary" #datepicker></mat-datepicker>
                <mat-error>
                    {{ errorMessage }}
                </mat-error>
            </mat-form-field>
            <mat-form-field
                [floatLabel]="'auto'"
                appearance="outline"
                class="datetime-time-field-custom-mat-field"
            >
                <div class="flex time-field">
                    <input
                        matInput
                        [matTimepicker]="timepicker"
                        [placeholder]="
                            'datetimepicker.datetime.placeholder' | translate
                        "
                        [value]="timeValue"
                        (valueChange)="onTimeChange($event)"
                        [matTimepickerMin]="timeMin()"
                        [matTimepickerMax]="timeMax()"
                        [errorStateMatcher]="errorMatcher"
                    />
                    <mat-timepicker-toggle
                        [for]="timepicker"
                    ></mat-timepicker-toggle>
                </div>
                <mat-timepicker color="primary" interval="1.0h" #timepicker />
                <mat-error>
                    {{ errorMessage }}
                </mat-error>
            </mat-form-field>
        </ng-container>
    }
</div>
