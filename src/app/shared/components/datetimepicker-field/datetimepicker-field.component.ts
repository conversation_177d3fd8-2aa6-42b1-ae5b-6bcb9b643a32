import { NgStyle } from '@angular/common'
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    forwardRef,
    Inject,
    inject,
    INJECTOR,
    Injector,
    input,
    OnInit,
    output,
    viewChild,
} from '@angular/core'
import {
    ControlValueAccessor,
    FormControl,
    FormGroupDirective,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'
import {
    DateAdapter,
    ErrorStateMatcher,
    provideNativeDateAdapter,
} from '@angular/material/core'
import {
    MatDatepickerInputEvent,
    MatDatepickerModule,
} from '@angular/material/datepicker'
import { MatError } from '@angular/material/form-field'
import { MatIcon } from '@angular/material/icon'
import { MatInput, MatInputModule } from '@angular/material/input'
import {
    MatTimepicker,
    MatTimepickerInput,
    MatTimepickerToggle,
} from '@angular/material/timepicker'
import { MatTooltip } from '@angular/material/tooltip'

import {
    CustomFieldErrorMatcher,
    NOOP_ERROR_MATCHER,
} from '@shared/custom-field-error-matcher'
import { isValidDate } from '@shared/utils/date-utils'

import { TranslatePipe, TranslateService } from '@ngx-translate/core'
import { merge, Subject, takeUntil } from 'rxjs'

export type DatepickerFormat = 'date' | 'datetime'

@Component({
    selector: 'app-datetimepicker-field',
    standalone: true,
    imports: [
        MatDatepickerModule,
        MatInputModule,
        MatTimepicker,
        MatTimepickerToggle,
        MatTimepickerInput,
        MatError,
        ReactiveFormsModule,
        MatIcon,
        MatTooltip,
        NgStyle,
        TranslatePipe,
    ],
    templateUrl: './datetimepicker-field.component.html',
    styleUrls: ['./datetimepicker-field.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => DatetimepickerFieldComponent),
            multi: true,
        },
        provideNativeDateAdapter(),
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DatetimepickerFieldComponent
    implements ControlValueAccessor, OnInit, AfterViewInit
{
    datepickerId = input('')
    label = input('')
    hint = input('')
    format = input<DatepickerFormat>('date')
    timeMin = input<string>('')
    timeMax = input<string>('')
    required = input<boolean>(false)

    valueChange = output<Date | null>()

    private onChange: (value: string | null) => void = () => undefined
    private onTouched: () => void = () => undefined

    private readonly destroy$ = new Subject<void>()

    private _value: Date | null = null
    protected _formControl: FormControl | undefined
    public errorMatcher: ErrorStateMatcher = NOOP_ERROR_MATCHER
    private readonly translate = inject(TranslateService)

    matInput = viewChild<MatInput>(MatInput)

    get requiredAsterisk(): boolean {
        return this._formControl?.hasValidator(Validators.required) ?? false
    }

    // https://stackoverflow.com/questions/45755958/how-to-get-formcontrol-instance-from-controlvalueaccessor
    constructor(
        @Inject(INJECTOR) private injector: Injector,
        private cdr: ChangeDetectorRef,
        private dateAdapter: DateAdapter<Date>,
        private translateService: TranslateService
    ) {}

    ngOnInit(): void {
        this.dateAdapter.setLocale(this.translateService.currentLang)
    }

    ngAfterViewInit(): void {
        // get control from the injector
        const ngControl: NgControl | null = this.injector.get(NgControl, null)
        const formDir = this.injector.get(FormGroupDirective, null)

        if (ngControl && formDir) {
            this._formControl = ngControl.control as FormControl
            this.errorMatcher = new CustomFieldErrorMatcher(this._formControl)

            // schedule for next tick, after Angular's own CD
            setTimeout(() => {
                this.cdr.detectChanges()
            }, 0)

            // re-draw whenever form.dirty changes
            merge(this._formControl.statusChanges, formDir.form.statusChanges)
                .pipe(takeUntil(this.destroy$))
                .subscribe(() => {
                    this.matInput()?.updateErrorState()
                    this.cdr.markForCheck()
                })
        }
    }

    get value(): Date | null {
        return this._value
    }

    set value(val: Date | null) {
        if (val !== this._value) {
            // don't set an invalid date
            if (val && !isValidDate(val)) {
                return
            }

            this._value = val

            let isoValue = null
            if (val !== null) {
                isoValue =
                    this.format() === 'date'
                        ? val.toISOString().split('T')[0]
                        : val.toISOString()
            }

            this.onChange(isoValue)
            this.onTouched()
            this.valueChange.emit(val)
        }
    }

    get errorMessage(): string {
        if (!this._formControl || !this._formControl.errors) {
            return ''
        }
        if (this._formControl.hasError('required')) {
            return this.translate.instant(
                'error.required-field.error-message-default'
            )
        }
        if (this._formControl.hasError('pattern')) {
            return this.translate.instant(
                'error.pattern-field.error-message-datetime'
            )
        }
        return this.translate.instant('error.field.error-message-default')
    }

    // https://github.com/angular/components/issues/30181 - combined datepicker and timepicker need separate values and be joined manually
    get dateValue(): Date | null {
        return this._value
    }

    set dateValue(newDate: Date | null) {
        // preserve time part
        if (newDate && this._value) {
            newDate.setHours(this._value.getHours(), this._value.getMinutes())
        }
        this.value = newDate
    }

    get timeValue(): Date | null {
        return this._value
    }

    set timeValue(newTime: Date | null) {
        // only update if the time truly changed
        if (!this._hasTimeChanged(newTime, this._value)) {
            return
        }

        const newDate = newTime ? new Date(this._value ?? Date.now()) : null
        if (newTime && newDate) {
            newDate.setHours(newTime.getHours(), newTime.getMinutes())
        }
        this.value = newDate
    }

    private _hasTimeChanged(a: Date | null, b: Date | null): boolean {
        if (!a && !b) return false // both null
        if (!a || !b) return true // one of them null
        return (
            a.getHours() !== b.getHours() || a.getMinutes() !== b.getMinutes()
        )
    }

    onDateChange(event: MatDatepickerInputEvent<Date>) {
        this.dateValue = event.value ?? null
        this.matInput()?.updateErrorState()
    }

    onTimeChange(timeDate: Date) {
        this.timeValue = timeDate
        this.matInput()?.updateErrorState()
    }

    onFocusOut(): void {
        this.onTouched()
        this.matInput()?.updateErrorState()
    }

    writeValue(value: string | null): void {
        this.onTouched()
        this._value = value ? new Date(value) : null
        this.cdr.markForCheck()
    }

    registerOnChange(fn: (value: string | null) => void): void {
        this.onChange = fn
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn
    }
}
