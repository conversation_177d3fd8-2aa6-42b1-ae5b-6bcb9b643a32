<h2 mat-dialog-title>{{ data.header }}</h2>

<mat-dialog-content>
    <p>{{ data.text }}</p>
    <mat-form-field appearance="fill" class="full-width">
        <mat-label>{{ data.label }}</mat-label>
        <input
            matInput
            [ngModel]="input()"
            (ngModelChange)="input.set($event)"
        />
    </mat-form-field>
</mat-dialog-content>

<mat-dialog-actions align="end">
    <app-button
        class="mr-1"
        variant="secondary"
        (buttonClick)="onCancel(); $event.stopPropagation()"
    >
        {{ 'dialog.default.cancel-button' | translate }}
    </app-button>
    <app-button
        (buttonClick)="onOk(); $event.stopPropagation()"
        cdkFocusInitial
    >
        {{ 'dialog.default.ok-button' | translate }}
    </app-button>
</mat-dialog-actions>
