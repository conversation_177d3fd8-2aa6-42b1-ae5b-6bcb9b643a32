import {
    ChangeDetectionStrategy,
    Component,
    inject,
    model,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import {
    MAT_DIALOG_DATA,
    MatDialogActions,
    MatDialogContent,
    MatDialogRef,
    MatDialogTitle,
} from '@angular/material/dialog'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatInputModule } from '@angular/material/input'

import { ButtonComponent } from '@shared/components/button/button.component'

import { TranslatePipe } from '@ngx-translate/core'

export interface InputDialogData {
    header: string
    text: string
    label: string
    input: string
}

@Component({
    selector: 'app-input-dialog',
    standalone: true,
    imports: [
        FormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatDialogContent,
        MatDialogActions,
        TranslatePipe,
        ButtonComponent,
        MatDialogTitle,
    ],
    templateUrl: './input-dialog.component.html',
    styleUrls: ['./input-dialog.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputDialogComponent {
    dialogRef = inject(MatDialogRef<InputDialogComponent, string>)
    data = inject<InputDialogData>(MAT_DIALOG_DATA)
    input = model(this.data.input)

    constructor() {
        this.dialogRef.disableClose = true
    }

    onCancel() {
        this.dialogRef.close()
    }

    onOk() {
        this.dialogRef.close(this.input())
    }
}
