<div [ngClass]="[class() + ' input-field-container']">
    <div class="flex mt-2 mb-1">
        <label class="mr-textIcon" [for]="inputId()">{{ label() }} </label>
        @if (requiredAsterisk) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Required icon"
                fontIcon="emergency"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'x-small' }"
            ></mat-icon>
        }
        @if (hint()) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Information icon"
                fontIcon="help_outline"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'medium' }"
            ></mat-icon>
        }
    </div>

    <mat-form-field
        floatLabel="auto"
        appearance="outline"
        class="input-field-custom-mat-field"
    >
        <input
            matInput
            [id]="inputId()"
            [type]="currentInputType"
            [placeholder]="placeholder()"
            [disabled]="disabled()"
            [value]="isEditing ? _value : formattedValue"
            [errorStateMatcher]="errorMatcher"
            [required]="required()"
            (focus)="onInputFieldFocus()"
            (blur)="onInputFieldBlur()"
            (input)="onInputFieldInput($event)"
        />
        @if (icon()) {
            <span matPrefix class="material-symbols-outlined select-none">
                {{ icon() }}
            </span>
        }

        @if (errorMessage) {
            <mat-error>{{ errorMessage }}</mat-error>
        }
    </mat-form-field>
</div>
