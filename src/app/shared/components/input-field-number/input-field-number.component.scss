.input-field-custom-mat-field {
    width: 100%;
    cursor: text;

    .mat-form-field-outline {
        border-color: var(--primary-color);
    }

    .mat-form-field-prefix .material-symbols-outlined {
        font-size: 24px;
        margin-right: 4px;
        cursor: default;
        user-select: none;
    }

    .mat-form-field-wrapper {
        padding: 0;
    }

    .mat-input-element {
        color: var(--primary-color);
        font-family: Cadiz-Regular, Arial, 'Helvetica Neue', sans-serif;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.6;

        &::placeholder {
            color: var(--Forms-textPlaceholder, #92b1bb);
        }
    }
}

.mat-icon {
    width: fit-content;
    height: fit-content;
}

label {
    height: fit-content;
}
