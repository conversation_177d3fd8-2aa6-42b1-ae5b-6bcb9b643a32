import { <PERSON><PERSON><PERSON>, Ng<PERSON>ty<PERSON> } from '@angular/common'
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    forwardRef,
    Inject,
    INJECTOR,
    Injector,
    input,
    OnDestroy,
    OnInit,
    output,
    PipeTransform,
    viewChild,
} from '@angular/core'
import {
    ControlValueAccessor,
    FormControl,
    FormGroupDirective,
    FormsModule,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'
import { ErrorStateMatcher } from '@angular/material/core'
import { MatIcon } from '@angular/material/icon'
import {
    MatError,
    MatFormField,
    MatInput,
    MatPrefix,
} from '@angular/material/input'
import { MatTooltip } from '@angular/material/tooltip'

import { LoggerService } from '@app/core/logging/logger.service'
import { I18nService } from '@app/shared/services/i18n.service'
import { LocaleCurrencyPipe } from '@app/shared/utils/custom-pipes/locale-currency.pipe'
import { LocaleDecimalPipe } from '@app/shared/utils/custom-pipes/locale-decimal.pipe'
import {
    CustomFieldErrorMatcher,
    NOOP_ERROR_MATCHER,
} from '@shared/custom-field-error-matcher'
import { getErrorMessage } from '@shared/utils/input-field-utils'

import { TranslateService } from '@ngx-translate/core'
import { merge, Subject, Subscription, takeUntil } from 'rxjs'

export type InputFieldType = 'number' | 'currency' | 'custom'

/**
 * Formats the displayed value when the input field is not in editing mode (blurred).
 * Integrates with Angular forms.
 * Handles locale-specific formatting for currency and numeric inputs.
 * Other pipes can be added for custom formatting
 */
@Component({
    selector: 'app-input-field-number',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        MatFormField,
        MatInput,
        MatError,
        MatPrefix,
        MatTooltip,
        MatIcon,
        FormsModule,
        NgClass,
        NgStyle,
    ],
    templateUrl: './input-field-number.component.html',
    styleUrl: './input-field-number.component.scss',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => InputFieldNumberComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputFieldNumberComponent
    implements ControlValueAccessor, AfterViewInit, OnInit, OnDestroy
{
    inputFieldType = input<InputFieldType>('number')
    inputId = input('')
    label = input('')
    hint = input('')
    icon = input<string | undefined>(undefined)
    placeholder = input('')
    class = input<string>('')
    disabled = input<boolean>(false)
    required = input<boolean>(false)
    customPipe = input<PipeTransform>()
    minDigits = input(0)
    maxDigits = input(0)

    valueChange = output<number | null>()

    private onChange: (value: number | null) => void = () => undefined
    private onTouched: () => void = () => undefined

    private readonly destroy$ = new Subject<void>()

    protected _value: string | null = null
    protected isEditing = false

    protected formattingFunction: (value: number | null) => string = (value) =>
        value?.toString() ?? ''
    get formattedValue(): string {
        return this.formattingFunction(this.value)
    }

    protected _formControl: FormControl | undefined
    public errorMatcher: ErrorStateMatcher = NOOP_ERROR_MATCHER
    private localeSubscription: Subscription

    private matInput = viewChild<MatInput>(MatInput)

    get requiredAsterisk(): boolean {
        return this._formControl?.hasValidator(Validators.required) ?? false
    }

    // https://stackoverflow.com/questions/45755958/how-to-get-formcontrol-instance-from-controlvalueaccessor
    constructor(
        @Inject(INJECTOR) private injector: Injector,
        private cdr: ChangeDetectorRef,
        private localeCurrencyPipe: LocaleCurrencyPipe,
        private localeDecimalPipe: LocaleDecimalPipe,
        private i18nService: I18nService,
        private translateService: TranslateService,
        private logger: LoggerService
    ) {
        this.localeSubscription = this.i18nService.currentLocale$.subscribe(
            () => {
                this.cdr.markForCheck()
            }
        )
    }

    ngOnInit(): void {
        this.configureFormattingFunction()
    }

    ngAfterViewInit(): void {
        // get control from the injector
        const ngControl: NgControl | null = this.injector.get(NgControl, null)
        const formDir = this.injector.get(FormGroupDirective, null)

        if (ngControl && formDir) {
            this._formControl = ngControl.control as FormControl
            this.errorMatcher = new CustomFieldErrorMatcher(this._formControl)

            // schedule for next tick, after Angular's own CD
            setTimeout(() => {
                this.cdr.detectChanges()
            }, 0)

            // re-draw whenever form.dirty changes
            merge(this._formControl.statusChanges, formDir.form.statusChanges)
                .pipe(takeUntil(this.destroy$))
                .subscribe(() => {
                    this.matInput()?.updateErrorState()
                    this.cdr.markForCheck()
                })
        }
    }

    ngOnDestroy(): void {
        this.localeSubscription.unsubscribe()
        this.destroy$.next()
        this.destroy$.complete()
    }

    public get value(): number | null {
        return this._value !== null ? parseFloat(this._value) : null
    }

    public set value(val: number | null) {
        if (val != null && typeof val != 'number') {
            this.logger.error(`Number input field value is not a number`)
            return
        }
        if (val !== this._value) {
            if (Number.isInteger(val) && !Number.isSafeInteger(val))
                this.logger.info(
                    `${val} is not a safe Number. There will be rounding errors`
                )

            const digits = this.getDigits(val)
            this._value =
                val !== null
                    ? val.toFixed(Math.min(this.maxDigits(), digits))
                    : null
            this.onChange(val)
            this.onTouched()
            this.valueChange.emit(val)
            this.matInput()?.updateErrorState()
            this.cdr.markForCheck()
        }
    }

    get currentInputType(): string {
        return this.isEditing ? 'number' : 'text'
    }

    public get errorMessage(): string {
        const message = getErrorMessage(this._formControl)
        return message ? this.translateService.instant(message) : message
    }

    private configureFormattingFunction() {
        let minDigits = this.minDigits()
        const maxDigits = this.maxDigits()

        if (minDigits > maxDigits) minDigits = maxDigits

        switch (this.inputFieldType()) {
            case 'currency': {
                this.formattingFunction = (value: number | null): string => {
                    if (value === null) return ''
                    const transformedVal = this.localeCurrencyPipe.transform(
                        value,
                        'EUR',
                        `1.${minDigits}-${maxDigits}`
                    )
                    return transformedVal ?? ''
                }
                return
            }
            case 'number': {
                this.formattingFunction = (value: number | null): string => {
                    if (value === null) return ''
                    const transformedVal = this.localeDecimalPipe.transform(
                        value,
                        `1.${minDigits}-${maxDigits}`
                    )
                    return transformedVal ?? ''
                }
                return
            }
            case 'custom': {
                this.formattingFunction = (value: number | null): string => {
                    if (value === null) return ''
                    return this.customPipe()?.transform(value) ?? ''
                }
                return
            }
            default: {
                this.formattingFunction = (value: number | null): string =>
                    value?.toString() ?? ''
                return
            }
        }
    }

    private getDigits(number: number | null) {
        if (number == null) return 0
        const numberString = number.toString()

        if (!numberString.includes('.')) {
            return 0
        }
        // Split the string by the decimal point and return the length of the fractional part
        return numberString.split('.')[1].length
    }

    protected onInputFieldBlur() {
        this.isEditing = false
        this.value = this._value ? parseFloat(this._value) : null
        this.onTouched()
        this.matInput()?.updateErrorState()
    }

    protected onInputFieldFocus() {
        this.isEditing = true
    }

    protected onInputFieldInput(event: Event): void {
        const target = event.target as HTMLInputElement
        this._value = target.value
        this.matInput()?.updateErrorState()
    }

    writeValue(value: number | null): void {
        this.value = value
    }

    registerOnChange(fn: (value: number | null) => void): void {
        this.onChange = fn
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn
    }
}
