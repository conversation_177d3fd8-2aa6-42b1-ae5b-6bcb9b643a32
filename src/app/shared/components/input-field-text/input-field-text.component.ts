import { <PERSON><PERSON><PERSON>, NgStyle } from '@angular/common'
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    forwardRef,
    Inject,
    INJECTOR,
    Injector,
    input,
    OnDestroy,
    OnInit,
    output,
    viewChild,
} from '@angular/core'
import {
    ControlValueAccessor,
    FormControl,
    FormGroupDirective,
    FormsModule,
    NG_VALUE_ACCESSOR,
    NgControl,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'
import { ErrorStateMatcher } from '@angular/material/core'
import { MatIcon } from '@angular/material/icon'
import {
    MatError,
    MatFormField,
    MatInput,
    MatPrefix,
} from '@angular/material/input'
import { MatTooltip } from '@angular/material/tooltip'

import {
    CustomFieldErrorMatcher,
    NOOP_ERROR_MATCHER,
} from '@shared/custom-field-error-matcher'
import { getErrorMessage } from '@shared/utils/input-field-utils'

import { TranslateService } from '@ngx-translate/core'
import { merge, Subject, takeUntil } from 'rxjs'

@Component({
    selector: 'app-input-field-text',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        MatFormField,
        MatInput,
        MatError,
        MatPrefix,
        MatTooltip,
        MatIcon,
        FormsModule,
        NgClass,
        NgStyle,
    ],
    templateUrl: './input-field-text.component.html',
    styleUrl: './input-field-text.component.scss',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => InputFieldTextComponent),
            multi: true,
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputFieldTextComponent
    implements ControlValueAccessor, AfterViewInit, OnInit, OnDestroy
{
    inputId = input('')
    label = input('')
    hint = input('')
    icon = input<string | undefined>(undefined)
    placeholder = input('')
    class = input<string>('')
    disabled = input<boolean>(false)
    required = input<boolean>(false)

    valueChange = output<string>()

    private onChange: (value: string) => void = () => undefined
    private onTouched: () => void = () => undefined

    private readonly destroy$ = new Subject<void>()

    private _value = ''
    protected _formControl: FormControl | undefined
    public errorMatcher: ErrorStateMatcher = NOOP_ERROR_MATCHER

    private matInput = viewChild<MatInput>(MatInput)

    get requiredAsterisk(): boolean {
        return this._formControl?.hasValidator(Validators.required) ?? false
    }

    // https://stackoverflow.com/questions/45755958/how-to-get-formcontrol-instance-from-controlvalueaccessor
    constructor(
        @Inject(INJECTOR) private injector: Injector,
        private cdr: ChangeDetectorRef,
        private translateService: TranslateService
    ) {}

    ngOnInit(): void {
        // nothing needed
    }

    ngAfterViewInit(): void {
        // get control from the injector
        const ngControl: NgControl | null = this.injector.get(NgControl, null)
        const formDir = this.injector.get(FormGroupDirective, null)

        if (ngControl && formDir) {
            this._formControl = ngControl.control as FormControl
            this.errorMatcher = new CustomFieldErrorMatcher(this._formControl)

            // schedule for next tick, after Angular's own CD
            setTimeout(() => {
                this.cdr.detectChanges()
            }, 0)

            // re-draw whenever form.dirty changes
            merge(this._formControl.statusChanges, formDir.form.statusChanges)
                .pipe(takeUntil(this.destroy$))
                .subscribe(() => {
                    this.matInput()?.updateErrorState()
                    this.cdr.markForCheck()
                })
        }
    }

    ngOnDestroy() {
        this.destroy$.next()
        this.destroy$.complete()
    }

    public get value(): string {
        return this._value
    }

    public set value(val: string) {
        if (val !== this._value) {
            this._value = val
            this.onChange(val)
            this.onTouched()
            this.valueChange.emit(val)
            this.matInput()?.updateErrorState()
            this.cdr.markForCheck()
        }
    }

    public get errorMessage(): string {
        const message = getErrorMessage(this._formControl)
        return message ? this.translateService.instant(message) : message
    }

    onInput(event: Event): void {
        const target = event.target as HTMLTextAreaElement
        this.value = target.value
        this.matInput()?.updateErrorState()
    }

    onFocusOut(): void {
        this.onTouched()
        this.matInput()?.updateErrorState()
    }

    writeValue(value: string): void {
        if (value === this._value) return

        this.onTouched()
        this._value = value || ''
        this.cdr.markForCheck()
    }

    registerOnChange(fn: (value: string) => void): void {
        this.onChange = fn
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn
    }

    protected readonly getErrorMessage = getErrorMessage
}
