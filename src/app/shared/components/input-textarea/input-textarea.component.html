<div class="input-textarea-field-container w-full h-full">
    <div class="flex mt-2 mb-1">
        <label class="mr-textIcon" [for]="inputId()">{{ label() }} </label>
        @if (requiredAsterisk) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Required icon"
                fontIcon="emergency"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'x-small' }"
            ></mat-icon>
        }
        @if (hint()) {
            <mat-icon
                [matTooltip]="hint()"
                [matTooltipPosition]="'above'"
                aria-hidden="false"
                aria-label="Information icon"
                fontIcon="help_outline"
                class="material-icons-outlined"
                [ngStyle]="{ 'font-size': 'medium' }"
            ></mat-icon>
        }
    </div>
    <mat-form-field
        [floatLabel]="'auto'"
        appearance="outline"
        class="input-textarea-field-custom-mat-field"
    >
        <textarea
            matInput
            [id]="inputId()"
            [rows]="rows()"
            [placeholder]="placeholder()"
            [value]="value"
            (input)="onInput($event)"
            (focusout)="onFocusOut()"
            [errorStateMatcher]="errorMatcher"
        ></textarea>
        <mat-error>{{
            'error.required-field.error-message-default' | translate
        }}</mat-error>
    </mat-form-field>
</div>
