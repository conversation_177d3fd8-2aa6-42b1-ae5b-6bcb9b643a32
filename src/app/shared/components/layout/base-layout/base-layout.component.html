<div class="base-layout">
    <header>
        <div class="header-container">
            <div class="navbar navbar-content flex items-center">
                <h2 class="brand m-0">AiTA</h2>

                <nav class="nav-items flex gap-8 ml-8">
                    <ng-container *ngTemplateOutlet="urlsFragment" />
                </nav>

                <div class="nav-mobile pl-2">
                    <app-button [matMenuTriggerFor]="navMenu" variant="plain">
                        <mat-icon class="text-white">menu</mat-icon>
                    </app-button>

                    <mat-menu #navMenu="matMenu">
                        <nav class="flex flex-col p-2 pl-4 pr-4 gap-4">
                            <ng-container *ngTemplateOutlet="urlsFragment" />
                        </nav>
                    </mat-menu>
                </div>

                <div class="ml-auto">
                    <app-user-profile-dropdown></app-user-profile-dropdown>
                </div>
            </div>
        </div>
    </header>

    <main class="flex-1 flex flex-col">
        <router-outlet></router-outlet>
    </main>

    <footer>
        <div class="footer-container">
            <h1 [routerLink]="ALL_TENDERS_URL">
                {{ 'base-header.title' | translate }}
            </h1>
        </div>
    </footer>
</div>

<ng-template #urlsFragment>
    <a [routerLink]="ALL_TENDERS_URL" routerLinkActive="router-link-active">
        {{ 'base-header.link.all-tenders' | translate }}
    </a>
    <!--    <a routerLink="/example" routerLinkActive="router-link-active">-->
    <!--        {{ 'base-header.link.example-page' | translate }}-->
    <!--    </a>-->
    @if (currentUrl === SYSTEM_CONFIGURATION_URL) {
        <a
            [routerLink]="SYSTEM_CONFIGURATION_URL"
            routerLinkActive="router-link-active"
        >
            {{ 'system-configuration.prompts.header' | translate }}
        </a>
    }
</ng-template>
