.base-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.navbar {
    width: 100%;
    padding: var(--margin-vertical-pad-large) 0;

    .nav-items {
        display: none;

        @media only screen and (min-width: 600px) {
            display: flex;
        }
    }

    .nav-mobile {
        display: block;

        @media only screen and (min-width: 600px) {
            display: none;
        }
    }

    .navbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
        padding: 0 1rem;

        /* Responsive max-width */
        max-width: 100%;

        /* Tablet (768px and up) */
        @media (min-width: 768px) {
            max-width: 720px;
            padding: 0 2rem;
        }

        /* Small laptop (1024px and up) */
        @media (min-width: 1024px) {
            max-width: 960px;
            padding: 0 2rem;
        }

        /* Large laptop (1280px and up) */
        @media (min-width: 1280px) {
            max-width: 1200px;
            padding: 0 3rem;
        }

        /* Desktop (1440px and up) */
        @media (min-width: 1440px) {
            max-width: 1360px;
            padding: 0 3rem;
        }

        /* Large desktop (1600px and up) */
        @media (min-width: 1600px) {
            max-width: 1520px;
            padding: 0 4rem;
        }

        /* Ultra-wide screens (1920px and up) */
        @media (min-width: 1920px) {
            max-width: 1800px;
            padding: 0 4rem;
        }

        /* 4K and ultra-wide (2560px and up) */
        @media (min-width: 2560px) {
            max-width: 2400px;
            padding: 0 6rem;
        }
    }

    h2 {
        font-family: Messina-Light, serif;
        letter-spacing: 2px;
        line-height: 125%;
        font-size: xx-large;
        color: var(--white);
        margin: 0;
    }

    a {
        color: var(--white);
        text-decoration: none;
        position: relative;
        font-family: Cadiz-Light, Arial, sans-serif;
        font-size: 18px;
        font-weight: 300;
        letter-spacing: -0.18px;
        line-height: 150%;
    }

    .router-link-active::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--white);
    }
}

header {
    height: var(--header-height);
    background: var(--primary-color);
    padding: 0 1rem;

    /* Responsive padding */
    @media (min-width: 768px) {
        padding: 0 2rem;
    }

    @media (min-width: 1280px) {
        padding: 0 3rem;
    }

    @media (min-width: 1600px) {
        padding: 0 4rem;
    }

    @media (min-width: 2560px) {
        padding: 0 6rem;
    }

    .header-container {
        margin: 0 auto;
        display: flex;
        align-items: center;

        /* Responsive max-width */
        max-width: 100%;

        @media (min-width: 768px) {
            max-width: 720px;
          padding: 0 2rem;
        }

        @media (min-width: 1024px) {
            max-width: 960px;
          padding: 0 2rem;
        }

        @media (min-width: 1280px) {
            max-width: 1200px;
          padding: 0 3rem;
        }

        @media (min-width: 1440px) {
            max-width: 1360px;
          padding: 0 3rem;
        }

        @media (min-width: 1600px) {
            max-width: 1520px;
          padding: 0 4rem;
        }

        @media (min-width: 1920px) {
            max-width: 1800px;
          padding: 0 4rem;
        }

        @media (min-width: 2560px) {
            max-width: 2400px;
        }
    }
}

main {
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
    background-color: var(--color-secondary-Silver-Planet, #f0f5f5);
}

footer {
    height: var(--footer-height);
    background: var(--primary-color);
    color: var(--white);
    padding: var(--margin-vertical-pad-large) 1rem;

    /* Responsive horizontal padding */
    @media (min-width: 768px) {
        padding: var(--margin-vertical-pad-large) 2rem;
    }

    @media (min-width: 1280px) {
        padding: var(--margin-vertical-pad-large) 3rem;
    }

    @media (min-width: 1600px) {
        padding: var(--margin-vertical-pad-large) 4rem;
    }

    @media (min-width: 2560px) {
        padding: var(--margin-vertical-pad-large) 6rem;
    }

    .footer-container {
        margin: 0 auto;
        display: flex;
        align-items: center;

      /* Responsive max-width */
      max-width: 100%;

      @media (min-width: 768px) {
        max-width: 720px;
        padding: 0 2rem;
      }

      @media (min-width: 1024px) {
        max-width: 960px;
        padding: 0 2rem;
      }

      @media (min-width: 1280px) {
        max-width: 1200px;
        padding: 0 3rem;
      }

      @media (min-width: 1440px) {
        max-width: 1360px;
        padding: 0 3rem;
      }

      @media (min-width: 1600px) {
        max-width: 1520px;
        padding: 0 4rem;
      }

      @media (min-width: 1920px) {
        max-width: 1800px;
        padding: 0 4rem;
      }

      @media (min-width: 2560px) {
        max-width: 2400px;
        padding: 0 6rem;
      }
    }

    h1 {
        cursor: pointer;
        user-select: none;
        margin: 0;
    }
}
