import { NgTemplateOutlet } from '@angular/common'
import { Component } from '@angular/core'
import { MatIcon } from '@angular/material/icon'
import { Mat<PERSON>enu, MatMenuTrigger } from '@angular/material/menu'
import { NavigationEnd, Router, RouterModule } from '@angular/router'

import {
    ALL_TENDERS_URL,
    SYSTEM_CONFIGURATION_URL,
} from '@core/auth/auth.constants'
import { UserProfileDropdownComponent } from '@modules/user-menu/pages/menu-modal/menu-modal.component'
import { ButtonComponent } from '@shared/components/button/button.component'

import { TranslateModule } from '@ngx-translate/core'
import { filter } from 'rxjs'

@Component({
    standalone: true,
    selector: 'app-base-layout',
    imports: [
        RouterModule,
        UserProfileDropdownComponent,
        TranslateModule,
        MatIcon,
        ButtonComponent,
        MatMenuTrigger,
        NgTemplateOutlet,
        MatMenu,
    ],
    templateUrl: './base-layout.component.html',
    styleUrl: './base-layout.component.scss',
})
export class BaseLayoutComponent {
    protected readonly ALL_TENDERS_URL = ALL_TENDERS_URL
    protected readonly SYSTEM_CONFIGURATION_URL = SYSTEM_CONFIGURATION_URL

    currentUrl = ''

    constructor(public router: Router) {
        // keep currentUrl up-to-date
        router.events
            .pipe(filter((e) => e instanceof NavigationEnd))
            .subscribe(
                (e: NavigationEnd) => (this.currentUrl = e.urlAfterRedirects)
            )
    }
}
