.page-upper {
  padding-top: 1rem;
  padding-bottom: 1rem;
  background-color: var(--white);

  .inner-wrapper {
    margin: 0 auto;
    padding: 0 1rem;

    /* Responsive max-width */
    max-width: 100%;

    /* Tablet (768px and up) */
    @media (min-width: 768px) {
      max-width: 720px;
      padding: 0 2rem;
    }

    /* Small laptop (1024px and up) */
    @media (min-width: 1024px) {
      max-width: 960px;
      padding: 0 2rem;
    }

    /* Large laptop (1280px and up) */
    @media (min-width: 1280px) {
      max-width: 1200px;
      padding: 0 3rem;
    }

    /* Desktop (1440px and up) */
    @media (min-width: 1440px) {
      max-width: 1360px;
      padding: 0 3rem;
    }

    /* Large desktop (1600px and up) */
    @media (min-width: 1600px) {
      max-width: 1520px;
      padding: 0 4rem;
    }

    /* Ultra-wide screens (1920px and up) */
    @media (min-width: 1920px) {
      max-width: 1800px;
      padding: 0 4rem;
    }

    /* 4K and ultra-wide (2560px and up) */
    @media (min-width: 2560px) {
      max-width: 2400px;
      padding: 0 6rem;
    }
  }
}

.page-lower {
  padding-top: 1rem;
  padding-bottom: 1rem;
  width: 100%;

  .inner-wrapper {
    margin: 0 auto;
    padding: 0 1rem 0;

    /* Responsive max-width */
    max-width: 100%;

    /* Tablet (768px and up) */
    @media (min-width: 768px) {
      max-width: 720px;
      padding: 0 2rem 0;
    }

    /* Small laptop (1024px and up) */
    @media (min-width: 1024px) {
      max-width: 960px;
      padding: 0 2rem 0;
    }

    /* Large laptop (1280px and up) */
    @media (min-width: 1280px) {
      max-width: 1200px;
      padding: 0 3rem 0;
    }

    /* Desktop (1440px and up) */
    @media (min-width: 1440px) {
      max-width: 1360px;
      padding: 0 3rem 0;
    }

    /* Large desktop (1600px and up) */
    @media (min-width: 1600px) {
      max-width: 1520px;
      padding: 0 4rem 0;
    }

    /* Ultra-wide screens (1920px and up) */
    @media (min-width: 1920px) {
      max-width: 1800px;
      padding: 0 4rem 0;
    }

    /* 4K and ultra-wide (2560px and up) */
    @media (min-width: 2560px) {
      max-width: 2400px;
      padding: 0 6rem 0;
    }
  }
}

/* Optional: If you want to create a shared mixin/utility class for consistent inner-wrapper behavior */
.responsive-inner-wrapper {
  margin: 0 auto;
  padding: 0 1rem;
  max-width: 100%;

  @media (min-width: 768px) {
    max-width: 720px;
    padding: 0 2rem;
  }

  @media (min-width: 1024px) {
    max-width: 960px;
    padding: 0 2rem;
  }

  @media (min-width: 1280px) {
    max-width: 1200px;
    padding: 0 3rem;
  }

  @media (min-width: 1440px) {
    max-width: 1360px;
    padding: 0 3rem;
  }

  @media (min-width: 1600px) {
    max-width: 1520px;
    padding: 0 4rem;
  }

  @media (min-width: 1920px) {
    max-width: 1800px;
    padding: 0 4rem;
  }

  @media (min-width: 2560px) {
    max-width: 2400px;
    padding: 0 6rem;
  }
}
