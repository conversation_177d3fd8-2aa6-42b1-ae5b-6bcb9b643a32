<div class="md-editor flex flex-col h-full border rounded-md overflow-hidden">
    <!-- Toolbar -->
    <div
        class="bg-white border-b px-1 py-1 flex flex-wrap items-center justify-between"
    >
        <div class="flex flex-wrap items-center flex-1 py-2">
            @for (action of toolbarActions; track action.title) {
                <app-button
                    variant="secondary"
                    size="small"
                    (buttonClick)="action.action()"
                    [matTooltip]="action.hintKey | translate"
                    [matTooltipPosition]="'above'"
                    class="px-2 py-1 text-sm flex items-center space-x-1 hover:bg-gray-100 no-mt"
                >
                    <span class="material-symbols-outlined text-md">
                        {{ action.icon }}
                    </span>
                </app-button>
            }
        </div>
    </div>

    <!-- Editor content -->
    <div class="editor-content flex">
        <div class="editor-panel">
            <div class="editor-header flex justify-between">
                <span class="editor-title">Markdown</span>
                <app-button
                    [cdkCopyToClipboard]="control().value"
                    (cdkCopyToClipboardCopied)="onCopyMarkdown()"
                    [matTooltip]="
                        'button.default.hint.copy-markdown' | translate
                    "
                    [matTooltipPosition]="'above'"
                    class="no-mt"
                    variant="secondary"
                    size="small"
                >
                    <span class="material-symbols-outlined">
                        {{ copyMarkdownSuccess() ? 'check' : 'content_copy' }}
                    </span>
                </app-button>
            </div>
            <textarea
                #markdown
                class="editor"
                [formControl]="control()"
                spellcheck="false"
            ></textarea>
        </div>

        <div class="preview-panel">
            <div class="editor-header flex justify-between">
                <span class="editor-title">{{
                    'md - editor.default.preview' | translate
                }}</span>
                <app-button
                    (buttonClick)="onCopyPreview()"
                    [matTooltip]="
                        'button.default.hint.copy-preview' | translate
                    "
                    [matTooltipPosition]="'above'"
                    class="no-mt"
                    variant="secondary"
                    size="small"
                >
                    <span class="material-symbols-outlined">
                        {{ copyPreviewSuccess() ? 'check' : 'content_copy' }}
                    </span>
                </app-button>
            </div>
            <div #preview class="preview">
                <markdown [data]="control().value"></markdown>
            </div>
        </div>
    </div>
</div>
