.md-editor {
    display: flex;
    flex-direction: column;
    height: 500px;
    border: 1px solid var(--border, #e2e8f0);
    border-radius: 8px;
    overflow: hidden;
}

//app-button:nth-child(1) {
//  padding-left: 0 !important;
//}
app-button {
    padding-left: 0 !important;
    margin-top: 0 !important;

    button {
        margin-top: 0 !important;
    }
}

#undefined .toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border);
    gap: 4px;
    flex-wrap: wrap;

    .toolbar-group {
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .toolbar-spacer {
        flex: 1;
    }

    .toolbar-btn {
        min-width: 32px;
        height: 32px;
        padding: 6px;
        border-radius: 4px;
        transition: background 0.15s ease;

        .material-symbols-outlined {
            font-size: 18px;
            line-height: 1;
        }

        &.success {
            color: var(--success, #22c55e);
            background: var(--success-light);
        }
    }
}

.editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.editor-panel,
.preview-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-header,
.preview-header {
    padding: 8px 16px;
    background: var(--muted);
    border-bottom: 1px solid var(--border);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--muted-foreground);
}

.preview-panel {
    border-left: 1px solid var(--border);
}

.editor {
    flex: 1;
    padding: 16px;
    border: none;
    outline: none;
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
    background: transparent;
    resize: none;

    &::placeholder {
        color: var(--muted-foreground);
    }
}

.preview {
    flex: 1;
    padding: 16px;
    overflow: auto;
    background: var(--background);
}

/* Responsive tweaks */
@media (max-width: 768px) {
    .md-editor {
        height: 400px;
    }
    .editor-content {
        flex-direction: column;
    }
    .preview-panel {
        border-left: none;
        border-top: 1px solid var(--border);
    }
    .toolbar {
        padding: 6px 8px;
        .toolbar-btn {
            min-width: 28px;
            height: 28px;
            padding: 4px;
            .material-symbols-outlined {
                font-size: 16px;
            }
        }
    }
}
