import { CdkCopyToClipboard, Clipboard } from '@angular/cdk/clipboard'
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    input,
    signal,
    ViewChild,
} from '@angular/core'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatTooltip } from '@angular/material/tooltip'

import { ButtonComponent } from '@shared/components/button/button.component'
import { DialogService } from '@shared/services/dialog.service'
import { writeHtmlAndText } from '@shared/utils/general-utils'

import { TranslatePipe, TranslateService } from '@ngx-translate/core'
import { MarkdownComponent } from 'ngx-markdown'
import { fromEvent, Subscription, throttleTime } from 'rxjs'

interface ToolbarAction {
    hintKey: string
    icon: string
    title: string
    action: () => void
    shortcut?: string
}

@Component({
    selector: 'app-md-editor',
    standalone: true,
    imports: [
        MarkdownComponent,
        ReactiveFormsModule,
        ButtonComponent,
        CdkCopyToClipboard,
        FormsModule,
        MatTooltip,
        TranslatePipe,
    ],
    templateUrl: './md-editor.component.html',
    styleUrl: './md-editor.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MdEditorComponent implements AfterViewInit {
    control = input.required<FormControl<string>>()
    original = input.required()

    @ViewChild('markdown', { static: true })
    markdownRef!: ElementRef<HTMLTextAreaElement>
    @ViewChild('preview', { static: true })
    previewRef!: ElementRef<HTMLDivElement>
    copyMarkdownSuccess = signal(false)
    copyPreviewSuccess = signal(false)

    private subscription!: Subscription

    constructor(
        private dialogService: DialogService,
        private translationService: TranslateService,
        private clipboard: Clipboard
    ) {}

    ngAfterViewInit() {
        // scroll events on the markdown textarea for syncing with preview textarea
        this.subscription = fromEvent(this.markdownRef.nativeElement, 'scroll')
            .pipe(throttleTime(10))
            .subscribe(() => this.syncScroll())

        // add keyboard shortcuts
        this.markdownRef.nativeElement.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault()
                        this.toggleFormat('**', '**', 'bold text')
                        break
                    case 'i':
                        e.preventDefault()
                        this.toggleFormat('*', '*', 'italic text')
                        break
                    case 'k':
                        e.preventDefault()
                        this.insertLink()
                        break
                }
            }
        })
    }

    private syncScroll() {
        const markdownRefEl = this.markdownRef.nativeElement
        const previewRefEl = this.previewRef.nativeElement

        const markdownRefElScrollTop = markdownRefEl.scrollTop
        const markdownRefElScrollHeight =
            markdownRefEl.scrollHeight - markdownRefEl.clientHeight
        const previewRefElScrollHeight =
            previewRefEl.scrollHeight - previewRefEl.clientHeight

        const scale = markdownRefElScrollHeight / previewRefElScrollHeight
        previewRefEl.scrollTop = markdownRefElScrollTop / scale
    }

    onCopyMarkdown() {
        this.copyMarkdownSuccess.set(true)
        setTimeout(() => this.copyMarkdownSuccess.set(false), 2000)
    }

    async onCopyPreview() {
        const el = this.previewRef.nativeElement
        const html = el.innerHTML
        const text = el.innerText

        let copied = false
        if ('clipboard' in navigator && typeof ClipboardItem !== 'undefined') {
            copied = await writeHtmlAndText(html, text)
        }

        if (!copied) {
            // fallback: plain-text
            this.clipboard.copy(text)
        }

        this.copyPreviewSuccess.set(true)
        setTimeout(() => this.copyPreviewSuccess.set(false), 2000)
    }

    toolbarActions: ToolbarAction[] = [
        {
            hintKey: 'md-toolbar.default.hint.bold',
            icon: 'format_bold',
            title: 'Bold',
            action: () => this.toggleFormat('**', '**', 'bold text'),
            shortcut: 'Ctrl+B',
        },
        {
            hintKey: 'md-toolbar.default.hint.italic',
            icon: 'format_italic',
            title: 'Italic',
            action: () => this.toggleFormat('*', '*', 'italic text'),
            shortcut: 'Ctrl+I',
        },
        {
            hintKey: 'md-toolbar.default.hint.strikethrough',
            icon: 'format_strikethrough',
            title: 'Strikethrough',
            action: () => this.toggleFormat('~~', '~~', 'strikethrough text'),
        },
        {
            hintKey: 'md-toolbar.default.hint.code',
            icon: 'code',
            title: 'Inline Code',
            action: () => this.toggleFormat('`', '`', 'code'),
        },
        {
            hintKey: 'md-toolbar.default.hint.heading1',
            icon: 'looks_one',
            title: 'Heading 1',
            action: () => this.toggleHeading(1),
        },
        {
            hintKey: 'md-toolbar.default.hint.heading2',
            icon: 'looks_two',
            title: 'Heading 2',
            action: () => this.toggleHeading(2),
        },
        {
            hintKey: 'md-toolbar.default.hint.heading3',
            icon: 'looks_3',
            title: 'Heading 3',
            action: () => this.toggleHeading(3),
        },
        {
            hintKey: 'md-toolbar.default.hint.quote',
            icon: 'format_quote',
            title: 'Quote',
            action: () => this.toggleQuote(),
        },
        {
            hintKey: 'md-toolbar.default.hint.unordered_list',
            icon: 'format_list_bulleted',
            title: 'Unordered List',
            action: () => this.toggleList('- '),
        },
        {
            hintKey: 'md-toolbar.default.hint.ordered_list',
            icon: 'format_list_numbered',
            title: 'Ordered List',
            action: () => this.toggleList('1. '),
        },
        {
            hintKey: 'md-toolbar.default.hint.link',
            icon: 'link',
            title: 'Link',
            action: () => this.insertLink(),
        },
        {
            hintKey: 'md-toolbar.default.hint.table',
            icon: 'table_chart',
            title: 'Table',
            action: () => this.insertTable(),
        },
        {
            hintKey: 'md-toolbar.default.hint.horizontal_rule',
            icon: 'horizontal_rule',
            title: 'Horizontal Rule',
            action: () => this.insertHorizontalRule(),
        },
    ]

    private getTextarea(): HTMLTextAreaElement {
        return this.markdownRef.nativeElement
    }

    private getSelectionInfo() {
        const textarea = this.getTextarea()
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const value = textarea.value
        const selectedText = value.substring(start, end)

        return { start, end, selectedText, value }
    }

    private updateTextarea(newValue: string, newStart: number, newEnd: number) {
        const markdownRefEl = this.markdownRef.nativeElement

        const previousScroll = markdownRefEl.scrollTop
        markdownRefEl.value = newValue
        this.control().setValue(newValue, {
            emitModelToViewChange: false,
            emitViewToModelChange: false,
            emitEvent: true,
        })

        setTimeout(() => {
            markdownRefEl.focus()
            markdownRefEl.setSelectionRange(newStart, newEnd)
            markdownRefEl.scrollTop = previousScroll
        }, 0)
    }

    private toggleFormat(prefix: string, suffix: string, placeholder: string) {
        const { start, end, selectedText, value } = this.getSelectionInfo()

        if (selectedText) {
            const beforeStart = Math.max(0, start - prefix.length)
            const afterEnd = Math.min(value.length, end + suffix.length)
            const beforePrefix = value.substring(beforeStart, start)
            const afterSuffix = value.substring(end, afterEnd)

            if (beforePrefix === prefix && afterSuffix === suffix) {
                const newValue =
                    value.substring(0, beforeStart) +
                    selectedText +
                    value.substring(afterEnd)
                this.updateTextarea(
                    newValue,
                    beforeStart,
                    beforeStart + selectedText.length
                )
            } else {
                const newValue =
                    value.substring(0, start) +
                    prefix +
                    selectedText +
                    suffix +
                    value.substring(end)
                this.updateTextarea(
                    newValue,
                    start + prefix.length,
                    start + prefix.length + selectedText.length
                )
            }
        } else {
            const newValue =
                value.substring(0, start) +
                prefix +
                placeholder +
                suffix +
                value.substring(end)
            this.updateTextarea(
                newValue,
                start + prefix.length,
                start + prefix.length + placeholder.length
            )
        }
    }

    private toggleHeading(level: number) {
        const { start, end, value } = this.getSelectionInfo()
        const lines = value.split('\n')

        // Find the line containing the cursor
        let currentPos = 0
        let lineIndex = 0

        for (let i = 0; i < lines.length; i++) {
            if (currentPos + lines[i].length >= start) {
                lineIndex = i
                break
            }
            currentPos += lines[i].length + 1
        }

        const line = lines[lineIndex]
        const headingPrefix = '#'.repeat(level) + ' '

        const existingHeadingMatch = line.match(/^#{1,6} /)

        if (existingHeadingMatch) {
            if (existingHeadingMatch[0] === headingPrefix) {
                lines[lineIndex] = line.replace(/^#{1,6} /, '')
            } else {
                lines[lineIndex] = line.replace(/^#{1,6} /, headingPrefix)
            }
        } else {
            lines[lineIndex] = headingPrefix + line
        }

        const newValue = lines.join('\n')
        this.updateTextarea(newValue, start, end)
    }

    private toggleQuote() {
        const { start, end, value } = this.getSelectionInfo()
        const lines = value.split('\n')

        let currentPos = 0
        const affectedLines: number[] = []

        for (let i = 0; i < lines.length; i++) {
            const lineStart = currentPos
            const lineEnd = currentPos + lines[i].length

            if (lineEnd >= start && lineStart <= end) {
                affectedLines.push(i)
            }

            currentPos += lines[i].length + 1
        }

        const allQuoted = affectedLines.every((i) => lines[i].startsWith('> '))

        affectedLines.forEach((i) => {
            if (allQuoted) {
                lines[i] = lines[i].replace(/^> /, '')
            } else {
                lines[i] = '> ' + lines[i]
            }
        })

        const newValue = lines.join('\n')
        this.updateTextarea(newValue, start, end)
    }

    private toggleList(prefix: string) {
        const { start, end, value } = this.getSelectionInfo()
        const lines = value.split('\n')

        let currentPos = 0
        const affectedLines: number[] = []

        for (let i = 0; i < lines.length; i++) {
            const lineStart = currentPos
            const lineEnd = currentPos + lines[i].length

            if (lineEnd >= start && lineStart <= end) {
                affectedLines.push(i)
            }

            currentPos += lines[i].length + 1
        }

        // are lines already list items?
        const listRegex = /^(\s*)([-*+]|\d+\.)\s/
        const taskRegex = /^(\s*)- \[[ x]\]\s/

        affectedLines.forEach((i) => {
            const line = lines[i]

            if (prefix.includes('[ ]')) {
                if (taskRegex.test(line)) {
                    lines[i] = line.replace(taskRegex, '$1')
                } else {
                    lines[i] = prefix + line
                }
            } else if (listRegex.test(line)) {
                // remove formatting
                lines[i] = line.replace(listRegex, '$1')
            } else {
                // add formatting
                lines[i] = prefix + line
            }
        })

        const newValue = lines.join('\n')
        this.updateTextarea(newValue, start, end)
    }

    private async insertLink() {
        const { start, end, selectedText, value } = this.getSelectionInfo()
        const linkText = selectedText || 'link text'

        const url = await this.dialogService.openInputDialog({
            header: this.translationService.instant('dialog.url.input-header'),
            text: this.translationService.instant('dialog.url.input-text'),
            label: this.translationService.instant('dialog.url.input-label'),
            input: 'https://',
        })

        if (!url) {
            return
        }

        const linkMarkdown = `[${linkText}](${url})`
        const newValue =
            value.substring(0, start) + linkMarkdown + value.substring(end)

        this.updateTextarea(newValue, start + 1, start + 1 + linkText.length)
    }

    private insertTable() {
        const { start, end, value } = this.getSelectionInfo()
        const tableMarkdown = `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |`

        const newValue =
            value.substring(0, start) + tableMarkdown + value.substring(end)
        this.updateTextarea(newValue, start, start + tableMarkdown.length)
    }

    private insertHorizontalRule() {
        const { start, end, value } = this.getSelectionInfo()
        const hr = '\n---\n'
        const newValue = value.substring(0, start) + hr + value.substring(end)
        this.updateTextarea(newValue, start + hr.length, start + hr.length)
    }
}
