<div
    class="modal-backdrop fixed inset-0 flex items-center justify-center bg-gray-950 bg-opacity-60 z-50"
    role="dialog"
    aria-modal="true"
    tabindex="-1"
    #modalContainer
>
    <div
        class="modal-container bg-white rounded w-full h-full p-verticalGap relative flex flex-col"
    >
        <!-- modal header -->
        <div class="modal-header flex justify-between w-full items-center">
            <div class="flex items-center gap-8">
                <h2>{{ title() }}</h2>
                <ng-content select="[headerButtons]"></ng-content>
            </div>
            <app-button
                variant="round"
                class="no-mt"
                (buttonClick)="onClose()"
                icon="close"
            ></app-button>
        </div>
        <app-separator separatorType="horizontal"></app-separator>

        <!-- scrollable content area -->
        <div class="content-container flex-1 overflow-y-auto p-verticalGap">
            <ng-content></ng-content>
        </div>

        <!-- fixed action buttons -->
        <div class="modal-actions flex justify-end space-x-2 mt-6">
            <ng-content select="[customButtons]"></ng-content>
            @if (useDefaultButtons()) {
                <app-button
                    (buttonClick)="onCancel()"
                    icon="close"
                    variant="secondary"
                    >{{ 'modal.cancel-button' | translate }}</app-button
                >
                <app-button
                    [disabled]="disableSave()"
                    (buttonClick)="onSave()"
                    icon="save"
                    >{{ 'modal.save-button' | translate }}</app-button
                >
            }
        </div>
    </div>
</div>
