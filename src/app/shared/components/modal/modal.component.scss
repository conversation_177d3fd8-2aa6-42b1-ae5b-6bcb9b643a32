.modal-backdrop {
    margin: 1.5rem auto 0;

    /* Default: Small screens and mobile */
    max-width: 100%;

    /* Tablet (768px and up) */
    @media (min-width: 768px) {
        max-width: 720px;
    }

    /* Small laptop (1024px and up) */
    @media (min-width: 1024px) {
        max-width: 960px;
    }

    /* Large laptop (1280px and up) */
    @media (min-width: 1280px) {
        max-width: 1200px;
    }

    /* Desktop (1440px and up) */
    @media (min-width: 1440px) {
        max-width: 1360px;
    }

    /* Large desktop (1600px and up) */
    @media (min-width: 1600px) {
        max-width: 1520px;
    }

    /* Ultra-wide screens (1920px and up) */
    @media (min-width: 1920px) {
        max-width: 1800px;
    }

    /* 4K and ultra-wide (2560px and up) */
    @media (min-width: 2560px) {
        max-width: 2400px;
    }

    .modal-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: var(--margin-vertical-gap);
    }

    .left-container {
        width: auto;
    }
}

h2 {
    color: var(--primary-color);
    font-family: Cadiz-Light, Arial, 'Helvetica Neue', sans-serif;
    font-size: 2rem;
    letter-spacing: -0.2px;
    font-weight: 300;
    line-height: 150%;
    margin: 0;
}
