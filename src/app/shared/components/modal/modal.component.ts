import {
    AfterViewInit,
    Component,
    ElementRef,
    input,
    output,
    ViewChild,
} from '@angular/core'

import { ButtonComponent } from '@shared/components/button/button.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'

import { TranslateModule } from '@ngx-translate/core'

@Component({
    selector: 'app-modal',
    imports: [SeparatorComponent, ButtonComponent, TranslateModule],
    templateUrl: './modal.component.html',
    styleUrl: './modal.component.scss',
    standalone: true,
})
export class ModalComponent implements AfterViewInit {
    title = input<string>('')
    useDefaultButtons = input<boolean>(true)
    disableSave = input<boolean>(false)
    saveClicked = output()
    cancelClicked = output()
    closeClicked = output()

    @ViewChild('modalContainer')
    private modalContainer!: ElementRef<HTMLDivElement>

    ngAfterViewInit(): void {
        this.modalContainer.nativeElement.focus()
    }

    onSave(): void {
        this.saveClicked.emit()
    }

    onCancel(): void {
        this.cancelClicked.emit()
    }

    onClose(): void {
        this.closeClicked.emit()
    }
}
