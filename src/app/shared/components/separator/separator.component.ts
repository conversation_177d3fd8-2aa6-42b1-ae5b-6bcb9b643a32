import { <PERSON><PERSON><PERSON>, NgStyle } from '@angular/common'
import { Component, input } from '@angular/core'

type SeparatorType = 'vertical' | 'horizontal'

@Component({
    selector: 'app-separator',
    imports: [<PERSON><PERSON>lass, NgStyle],
    templateUrl: './separator.component.html',
    styleUrl: './separator.component.scss',
    standalone: true,
})
export class SeparatorComponent {
    separatorType = input<SeparatorType>('horizontal')
    color = input('var(--primary-highlighted-color)')
}
