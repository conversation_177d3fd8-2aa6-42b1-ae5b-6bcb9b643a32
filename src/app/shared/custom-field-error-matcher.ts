import { FormControl, FormGroupDirective, NgForm } from '@angular/forms'
import { ErrorStateMatcher } from '@angular/material/core'

export class CustomFieldErrorMatcher implements ErrorStateMatcher {
    constructor(private customControl: FormControl | undefined) {}

    isErrorState(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _control: FormControl | null,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _form: FormGroupDirective | NgForm | null
    ): boolean {
        if (!this.customControl) {
            return false
        }
        return (
            this.customControl.invalid &&
            (this.customControl.dirty || this.customControl.touched)
        )
    }
}

export const NOOP_ERROR_MATCHER: ErrorStateMatcher = {
    isErrorState: () => false,
}
