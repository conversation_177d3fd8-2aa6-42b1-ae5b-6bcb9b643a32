import { Injectable } from '@angular/core'
import { MatPaginatorIntl } from '@angular/material/paginator'

import { TranslateService } from '@ngx-translate/core'
import { Subject } from 'rxjs'

@Injectable()
export class CustomPaginatorIntl extends MatPaginatorIntl {
    override changes = new Subject<void>()

    constructor(private translate: TranslateService) {
        super()
        this.initTranslations()

        this.translate.onLangChange.subscribe(() => {
            this.initTranslations()
        })
    }

    override getRangeLabel = (
        page: number,
        pageSize: number,
        length: number
    ) => {
        if (length === 0 || pageSize === 0) {
            return this.translate.instant('paginator.rangePageLabelEmpty', {
                length,
            })
        }
        const startIndex = page * pageSize
        const endIndex = Math.min(startIndex + pageSize, length)
        return this.translate.instant('paginator.rangePageLabel', {
            start: startIndex + 1,
            end: endIndex,
            length,
        })
    }

    private initTranslations(): void {
        this.translate
            .get([
                'paginator.itemsPerPageLabel',
                'paginator.nextPageLabel',
                'paginator.previousPageLabel',
                'paginator.firstPageLabel',
                'paginator.lastPageLabel',
            ])
            .subscribe((translations) => {
                this.itemsPerPageLabel =
                    translations['paginator.itemsPerPageLabel']
                this.nextPageLabel = translations['paginator.nextPageLabel']
                this.previousPageLabel =
                    translations['paginator.previousPageLabel']
                this.firstPageLabel = translations['paginator.firstPageLabel']
                this.lastPageLabel = translations['paginator.lastPageLabel']

                this.changes.next()
            })
    }
}
