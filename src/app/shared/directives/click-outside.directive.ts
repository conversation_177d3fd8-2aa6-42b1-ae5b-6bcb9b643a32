import {
    Directive,
    ElementRef,
    EventEmitter,
    HostListener,
    Output,
} from '@angular/core'

@Directive({
    selector: '[appClickOutside]',
    standalone: true,
})
export class ClickOutsideDirective {
    @Output() clickOutside = new EventEmitter<void>()

    constructor(private elementRef: ElementRef) {}

    @HostListener('document:click', ['$event.target'])
    onClick(target: HTMLElement): void {
        const targetElement = this.elementRef.nativeElement
        if (targetElement && !targetElement.contains(target)) {
            this.clickOutside.emit()
        }
    }

    @HostListener('document:keydown.escape', ['$event'])
    onEscapeKey() {
        this.clickOutside.emit()
    }
}
