import {
    Directive,
    EventEmitter,
    HostBinding,
    HostListener,
    Output,
} from '@angular/core'

@Directive({
    selector: '[appDragAndDrop]',
    standalone: true,
})
export class DragAndDropDirective {
    @Output() fileDropped = new EventEmitter<FileList>()
    @HostBinding('class.highlight') isDragging = false

    @HostListener('dragover', ['$event'])
    onDragOver(event: DragEvent) {
        event.preventDefault()
        this.isDragging = true
    }

    @HostListener('dragleave', ['$event'])
    onDragLeave(event: DragEvent) {
        event.preventDefault()
        this.isDragging = false
    }

    @HostListener('drop', ['$event'])
    onDrop(event: DragEvent) {
        event.preventDefault()
        this.isDragging = false

        if (event.dataTransfer) {
            this.fileDropped.emit(event.dataTransfer.files)
        }
    }
}
