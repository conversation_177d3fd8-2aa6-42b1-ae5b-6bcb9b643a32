import { HttpErrorResponse } from '@angular/common/http'
import { Injectable } from '@angular/core'

import { ErrorDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'

import { catchError, OperatorFunction, throwError } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class ApiErrorHandlerService {
    constructor(private logger: LoggerService) {}

    private isErrorDTO(error: any): error is ErrorDTO {
        return !!error && typeof error.errorMessage === 'string'
    }

    private formatServerError(error: HttpErrorResponse): string {
        let formattedMessage = `An error with status ${error.status} occurred`

        if (this.isErrorDTO(error.error)) {
            const errorDTO = error.error
            formattedMessage = `Error status ${error.status}: ${errorDTO.errorMessage} `
            if (
                Array.isArray(errorDTO.details) &&
                errorDTO.details.length > 0
            ) {
                formattedMessage += ' — ' + errorDTO.details.join('; ')
            }
        }

        return formattedMessage
    }

    public handleError<T>(operation: string): OperatorFunction<T, T> {
        return catchError((error: any) => {
            let errorMsg = `Unexpected error during ${operation}: ${error.message || error}`

            if (error instanceof HttpErrorResponse) {
                // client-side error
                if (error.error instanceof ErrorEvent) {
                    errorMsg = `Client-side error during ${operation}: ${error.error.message}`
                } else {
                    errorMsg = `Server-side error during ${operation}: ${this.formatServerError(error)}`
                }
            }

            this.logger.error(errorMsg)

            return throwError(() => error)
        })
    }
}
