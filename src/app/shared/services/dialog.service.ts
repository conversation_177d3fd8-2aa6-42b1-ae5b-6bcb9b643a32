import { Injectable } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'

import {
    InputDialogComponent,
    InputDialogData,
} from '@shared/components/input-dialog/input-dialog.component'

import {
    ConfirmDialogComponent,
    ConfirmDialogData,
} from '../components/confirm-dialog/confirm-dialog.component'

import { TranslateService } from '@ngx-translate/core'
import { firstValueFrom } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class DialogService {
    constructor(
        private dialog: MatDialog,
        private translate: TranslateService
    ) {}

    async openConfirmDialog(header?: string, text?: string) {
        const dialogData: ConfirmDialogData = {
            header:
                header ??
                this.translate.instant('confirm-dialog.unsaved-changes.header'),
            text:
                text ??
                this.translate.instant('confirm-dialog.unsaved-changes.text'),
        }

        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
            data: dialogData,
        })

        return await firstValue<PERSON>rom(dialogRef.afterClosed())
    }

    async openInputDialog(config: InputDialogData): Promise<string | null> {
        const dialogData: InputDialogData = {
            header:
                config.header ??
                this.translate.instant('dialog.default.input-header'),
            text:
                config.text ??
                this.translate.instant('dialog.default.input-text'),
            label:
                config.label ??
                this.translate.instant('dialog.default.input-label'),
            input: config.input ?? '',
        }

        const ref = this.dialog.open(InputDialogComponent, {
            data: dialogData,
            disableClose: true,
        })

        const result = await firstValueFrom(ref.afterClosed())
        return typeof result === 'string' ? result : null
    }
}
