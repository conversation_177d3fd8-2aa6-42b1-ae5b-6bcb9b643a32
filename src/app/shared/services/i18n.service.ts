import { Injectable } from '@angular/core'
import { DateAdapter } from '@angular/material/core'
import { Router } from '@angular/router'

import { TranslateService } from '@ngx-translate/core'
import { BehaviorSubject } from 'rxjs'

export type AvailableLanguages = 'en' | 'de' | 'fi'

@Injectable({
    providedIn: 'root',
})
export class I18nService {
    private readonly storageKey = 'appLanguage'
    supportedLanguages: AvailableLanguages[] = ['en', 'de', 'fi']

    private currentLocaleSubject = new BehaviorSubject<string>('en-GB')
    currentLocale$ = this.currentLocaleSubject.asObservable()

    constructor(
        private translate: TranslateService,
        private dateAdapter: DateAdapter<Date>,
        private router: Router
    ) {
        translate.onLangChange.subscribe(() => {
            void this.router.navigateByUrl(this.router.url)
        })
    }

    init() {
        this.translate.addLangs(['de', 'en', 'fi'])
        this.translate.setDefaultLang('en')

        const savedLang = localStorage.getItem(
            this.storageKey
        ) as AvailableLanguages

        const browserLangRaw = this.translate.getBrowserLang()
        const browserLang: AvailableLanguages | undefined = browserLangRaw
            ? this.supportedLanguages.find((lang) =>
                  browserLangRaw.startsWith(lang)
              )
            : undefined

        const currentLang = savedLang || browserLang || 'en'

        this.setLanguage(currentLang)
    }

    getCurrentLocale(): string {
        return this.currentLocaleSubject.value
    }

    getCurrentLanguage(): AvailableLanguages {
        return this.translate.currentLang as AvailableLanguages
    }

    getCurrentLanguageISO(lang: string = this.translate.currentLang): string {
        switch (lang) {
            case 'de':
                return 'de-DE'
            case 'fi':
                return 'fi-FI'
            case 'en':
            default:
                return 'en-GB'
        }
    }

    setLanguage(lang: AvailableLanguages) {
        this.translate.use(lang)
        localStorage.setItem(this.storageKey, lang)

        const newLocale = this.getCurrentLanguageISO(lang)
        this.dateAdapter.setLocale(newLocale)
        this.currentLocaleSubject.next(newLocale)
    }
}
