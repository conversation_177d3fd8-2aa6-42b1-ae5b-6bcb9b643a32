import { inject, Injectable } from '@angular/core'
import { MatSnackBar } from '@angular/material/snack-bar'

import { TranslateService } from '@ngx-translate/core'

@Injectable({
    providedIn: 'root',
})
export class PopUpService {
    private _snackBar = inject(MatSnackBar)
    private readonly translate = inject(TranslateService)

    public openSuccessPopUp(
        action: string,
        messageKey = 'popup.success.message',
        duration = 10000
    ) {
        const translatedMessage = this.translate.instant(messageKey)
        this.openSnackBar(translatedMessage, action, duration, ['success'])
    }

    public openErrorPopUp(
        action: string,
        messageKey = 'popup.error.message',
        duration = 50000
    ) {
        const translatedMessage = this.translate.instant(messageKey)
        this.openSnackBar(translatedMessage, action, duration, ['error'])
    }

    private openSnackBar(
        message: string,
        action: string,
        duration: number,
        panelClassAdditions: string[]
    ) {
        this._snackBar.open(message, action, {
            panelClass: ['popup', ...panelClassAdditions],
            duration: duration,
        })
    }
}
