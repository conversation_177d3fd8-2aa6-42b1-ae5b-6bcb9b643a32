import { Injectable } from '@angular/core'

import { SystemConfigurationDTO } from '@app/api'
import { ConfigApiService } from '@app/api/api/config.service'
import { ApiErrorHandlerService } from '@shared/services/api-error-handler.service'

import { Observable } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class SystemConfigurationService {
    constructor(
        private configurationService: ConfigApiService,
        private apiErrorHandler: ApiErrorHandlerService
    ) {}

    getSystemConfig(): Observable<SystemConfigurationDTO> {
        return this.configurationService
            .getSystemConfiguration()
            .pipe(
                this.apiErrorHandler.handleError<SystemConfigurationDTO>(
                    'getSystemConfig'
                )
            )
    }

    updateSystemConfig(
        systemConfiguration: SystemConfigurationDTO
    ): Observable<SystemConfigurationDTO> {
        return this.configurationService
            .updateSystemConfiguration(systemConfiguration)
            .pipe(
                this.apiErrorHandler.handleError<SystemConfigurationDTO>(
                    'updateSystemConfig'
                )
            )
    }
}
