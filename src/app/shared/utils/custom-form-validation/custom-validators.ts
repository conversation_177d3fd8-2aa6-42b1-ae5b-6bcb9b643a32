import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms'

import {
    REGEX_ISO8601_DATE,
    REGEX_ISO8601_DATETIME,
    REGEX_ONLY_NUMBERS,
    REGEX_VALID_URL,
} from '@shared/utils/custom-form-validation/validation.constants'

export function iso8601DatetimeValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value
        if (!value) return null
        return REGEX_ISO8601_DATETIME.test(value)
            ? null
            : { iso8601DatetimeInvalid: { value } }
    }
}

export function iso8601DateValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value
        if (!value) return null
        return REGEX_ISO8601_DATE.test(value)
            ? null
            : { iso8601DateInvalid: { value } }
    }
}

export function onlyNumbersValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value
        if (!value) return null
        return REGEX_ONLY_NUMBERS.test(value)
            ? null
            : { onlyNumbersInvalid: { value } }
    }
}

export function validUrlValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value
        if (!value) return null
        return REGEX_VALID_URL.test(value)
            ? null
            : { validUrlInvalid: { value } }
    }
}
