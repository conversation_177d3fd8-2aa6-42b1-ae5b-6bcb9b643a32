export const REGEX_ISO8601_DATETIME = new RegExp(
    '^' +
        '(?:[1-9]\\d{3}-' + // year 1000–9999
        '(?:' +
        '(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])' + // months with 28 days
        '|(?:0[13-9]|1[0-2])-(?:29|30)' + // months with 30 days
        '|(?:0[13578]|1[02])-31' + // months with 31 days
        ')' +
        '|(?:' + // leap‐year (Feb 29)
        '(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])' + // e.g. 2024, 2400, etc.
        '|(?:[2468][048]|[13579][26])00)-02-29' +
        ')' +
        ')' +
        'T' + // literal “T”
        '(?:[01]\\d|2[0-3]):' + // hour (00–23)
        '[0-5]\\d:' + // minute (00–59)
        '[0-5]\\d' + // second (00–59)
        '(?:\\.\\d{1,9})?' + // optional fraction .SSS… up to 9 digits
        '(?:Z|[+-][01]\\d:[0-5]\\d)?' + // <--- make “Z” or “±hh:mm” optional
        '$'
)

export const REGEX_ISO8601_DATE = new RegExp(
    '^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)$'
)

export const REGEX_ONLY_NUMBERS = new RegExp('^[0-9]+$')
export const REGEX_NOT_NUMBERS = new RegExp(/[^\d.-]/g)

export const REGEX_VALID_URL = new RegExp(
    '^(http(s)?:\\/\\/)(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.' +
        '[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)$'
)
