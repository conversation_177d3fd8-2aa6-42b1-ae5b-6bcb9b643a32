import { CurrencyPipe } from '@angular/common'
import {
    ChangeDetectorRef,
    OnDestroy,
    Pipe,
    PipeTransform,
} from '@angular/core'

import { I18nService } from '@shared/services/i18n.service'

import { Subscription } from 'rxjs'

@Pipe({
    name: 'localeCurrency',
    pure: false,
    standalone: true,
})
export class LocaleCurrencyPipe implements PipeTransform, OnDestroy {
    private locale: string
    private subscription: Subscription

    constructor(
        private currencyPipe: CurrencyPipe,
        private i18nService: I18nService,
        private cdr: ChangeDetectorRef
    ) {
        this.locale = this.i18nService.getCurrentLocale()
        this.subscription = this.i18nService.currentLocale$.subscribe(
            (newLocale) => {
                this.locale = newLocale
                this.cdr.markForCheck()
            }
        )
    }

    transform(
        value: number | null | undefined,
        currencyCode = 'EUR',
        digitsInfo?: string
    ): string | null {
        if (value == null || undefined) {
            return null
        }

        return this.currencyPipe.transform(
            value,
            currencyCode,
            'symbol',
            digitsInfo,
            this.locale
        )
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe()
    }
}
