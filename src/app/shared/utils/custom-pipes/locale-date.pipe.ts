import { formatDate } from '@angular/common'
import {
    ChangeDetectorRef,
    OnDestroy,
    Pipe,
    PipeTransform,
} from '@angular/core'

import { I18nService } from '@shared/services/i18n.service'

import { Subscription } from 'rxjs'

@Pipe({
    name: 'localeDate',
    pure: false,
    standalone: true,
    // important so it updates on locale changes
})
export class LocaleDatePipe implements PipeTransform, OnDestroy {
    private locale: string
    private subscription: Subscription

    constructor(
        private i18nService: I18nService,
        private cdr: ChangeDetectorRef
    ) {
        this.locale = this.i18nService.getCurrentLocale()

        this.subscription = this.i18nService.currentLocale$.subscribe(
            (newLocale) => {
                this.locale = newLocale
                this.cdr.markForCheck()
            }
        )
    }

    transform(
        value: Date | string | number | null | undefined,
        format = 'shortDate'
    ): string {
        if (value == null) {
            return ''
        }
        return formatDate(value, format, this.locale)
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe()
    }
}
