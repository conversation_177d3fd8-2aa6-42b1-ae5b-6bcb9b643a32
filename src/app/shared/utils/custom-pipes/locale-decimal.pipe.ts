import { DecimalPipe } from '@angular/common'
import {
    ChangeDetectorRef,
    OnDestroy,
    Pipe,
    PipeTransform,
} from '@angular/core'

import { I18nService } from '@shared/services/i18n.service'

import { Subscription } from 'rxjs'

@Pipe({
    name: 'localeDecimal',
    pure: false,
    standalone: true,
})
export class LocaleDecimalPipe implements PipeTransform, OnDestroy {
    private locale: string
    private localeSubscription: Subscription

    constructor(
        private decimalPipe: DecimalPipe,
        private i18nService: I18nService,
        private cdr: ChangeDetectorRef
    ) {
        this.locale = this.i18nService.getCurrentLocale()
        this.localeSubscription = this.i18nService.currentLocale$.subscribe(
            (newLocale) => {
                this.locale = newLocale
                this.cdr.markForCheck()
            }
        )
    }

    transform(
        value: number | null | undefined,
        digitsInfo?: string
    ): string | null {
        if (value == null || undefined) {
            return null
        }

        return this.decimalPipe.transform(value, digitsInfo, this.locale)
    }

    ngOnDestroy(): void {
        this.localeSubscription.unsubscribe()
    }
}
