import { Pipe, PipeTransform } from '@angular/core'

@Pipe({
    name: 'starIcons',
    standalone: true,
})
export class StarIconsPipe implements PipeTransform {
    /**
     * Transforms a rating (0–total) into an array of
     * icon names ['star', 'star', 'star_border', …]
     */
    transform(rating: number, totalStars = 5): string[] {
        const icons: string[] = []
        for (let i = 1; i <= totalStars; i++) {
            icons.push(i <= rating ? 'star' : 'star_border')
        }
        return icons
    }
}
