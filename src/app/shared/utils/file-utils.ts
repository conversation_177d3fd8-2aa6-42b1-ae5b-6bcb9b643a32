import {
    FileStatus,
    TenderFileItem,
    TenderMetadataFileItem,
} from '@modules/tenders/models/tender-file.model'

export function getIconForFile(file: File | string): string {
    const name = typeof file === 'string' ? file : file.name
    const ext = getExtension(name)
    switch (ext) {
        case 'doc':
        case 'docx':
            return 'file-word-outline'
        case 'xls':
        case 'xlsx':
            return 'file-excel-outline'
        case 'pdf':
            return 'file-pdf-box'
        case 'zip':
            return 'folder-zip-outline'
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
            return 'file-image-outline'
        default:
            return 'clipboard-file-outline'
    }
}

export function getFileIcon(
    fileItem: TenderFileItem | TenderMetadataFileItem
): { icon: string; color?: string; class?: string; tooltip?: string } {
    switch (fileItem.status) {
        case FileStatus.TooLarge:
            return {
                icon: 'warning',
                color: 'warn',
                tooltip: getErrorHint(fileItem.status),
            }
        case FileStatus.Uploading:
            return {
                icon: 'progress_activity',
                class: 'primary-color-highlighted material-symbols-outlined',
            }
        case FileStatus.Downloading:
        case FileStatus.Deleting:
            return {
                icon: 'progress_activity',
                class: 'disabled-color material-symbols-outlined',
            }
        case FileStatus.ErrorUpload:
        case FileStatus.ErrorDownload:
        case FileStatus.ErrorDelete:
            return {
                icon: 'error_outline',
                color: 'warn',
                tooltip: getErrorHint(fileItem.status),
            }
        case FileStatus.New:
            return {
                icon: 'check_circle',
                class: 'success-color material-symbols-outlined',
            }
        case FileStatus.SuccessUpload:
        case FileStatus.SuccessDownload:
        case FileStatus.Uploaded:
            return {
                icon: 'cloud_done',
                class: 'disabled-color material-symbols-outlined',
            }
        case FileStatus.Pending:
        default:
            return {
                icon: 'arrow_upload_progress',
                class: 'primary-color-highlighted material-symbols-outlined',
            }
    }
}

function getErrorHint(status: string): string {
    switch (status) {
        case 'errorDelete':
            return 'tender.documents.hint.file-delete-error'
        case 'errorDownload':
            return 'tender.documents.hint.file-download-error'
        case 'errorUpload':
            return 'tender.documents.hint.file-upload-error'
        case 'tooLarge':
            return 'tender.documents.error.too-large'
        default:
            return ''
    }
}

export interface AllowedFileConfig {
    allowedMimeTypes: string[]
    allowedExtensions: string[]
}

export function getAllowedFileConfig(): AllowedFileConfig {
    return {
        allowedMimeTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/png',
            'image/jpeg',
            'application/zip',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/markdown',
            'application/vnd.oasis.opendocument.text',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/rtf',
            'text/plain',
        ],
        allowedExtensions: [
            'pdf',
            'doc',
            'docx',
            'png',
            'jpg',
            'jpeg',
            'zip',
            'xls',
            'xlsx',
            'md',
            'odt',
            'ppt',
            'pptx',
            'rft',
            'txt',
        ],
    }
}

export function isAllowedFile(
    file: File,
    config: AllowedFileConfig = getAllowedFileConfig()
): boolean {
    if (config.allowedMimeTypes.includes(file.type)) {
        return true
    }
    const ext = file.name.split('.').pop()?.toLowerCase()
    return ext ? config.allowedExtensions.includes(ext) : false
}

export function getAcceptedFiles(): string {
    const config = getAllowedFileConfig()
    return config.allowedExtensions.map((ext) => `.${ext}`).join(',')
}

export function getExtension(filename: string): string {
    const idx = filename.lastIndexOf('.')
    if (idx < 0) return ''

    return filename.substring(idx + 1).toLowerCase()
}
