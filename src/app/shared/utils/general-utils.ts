/**
 * Write both HTML and plain-text to the clipboard if possible.
 * @param html Rich HTML string
 * @param text Plain-text string
 */
export async function writeHtmlAndText(
    html: string,
    text: string
): Promise<boolean> {
    if ('clipboard' in navigator && typeof ClipboardItem !== 'undefined') {
        try {
            await navigator.clipboard.write([
                new ClipboardItem({
                    'text/html': new Blob([html], { type: 'text/html' }),
                    'text/plain': new Blob([text], { type: 'text/plain' }),
                }),
            ])
            return true
        } catch (e) {
            console.warn(
                'HTML clipboard write failed, falling back to plain text',
                e
            )
            return false
        }
    }
    return false
}
