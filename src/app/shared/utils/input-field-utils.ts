import { FormControl } from '@angular/forms'

const customErrorMessageMap: Record<string, string> = {
    iso8601DatetimeInvalid: 'error.pattern-field.error-message-datetime',
    iso8601DateInvalid: 'error.pattern-field.error-message-date',
    onlyNumbersInvalid: 'error.pattern-field.error-message-number',
    validUrlInvalid: 'error.pattern-field.error-message-url',
}

export function getErrorMessage(formControl: FormControl | undefined): string {
    if (!formControl || !formControl.errors) {
        return ''
    }
    if (formControl.hasError('required')) {
        return 'error.required-field.error-message-default'
    }
    if (formControl.hasError('pattern')) {
        return 'error.pattern-field.error-message-pattern'
    }

    for (const errorKey in formControl.errors) {
        if (customErrorMessageMap[errorKey]) {
            return customErrorMessageMap[errorKey]
        }
    }

    return 'error.field.error-message-default'
}
