import { computed, inject, signal } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'

export class URLQueryParamStore<Keys extends readonly string[]> {
    private router = inject(Router)
    private route = inject(ActivatedRoute)

    public get = computed(() => this.queryParams())

    private readonly queryParams = signal<
        Record<Keys[number], string | undefined>
    >({} as Record<Keys[number], string>)

    constructor(paramNames: Keys) {
        const initial: Record<Keys[number], string | undefined> = {} as any
        paramNames.forEach((p: Keys[number]) => {
            initial[p] = undefined
        })

        this.queryParams.set(initial)

        // Watch the route query params and update the signal
        this.route.queryParams.subscribe((params) => {
            const newValue: Record<Keys[number], string | undefined> = {} as any
            paramNames.forEach((p: Keys[number]) => {
                if (params[p]) {
                    newValue[p] = params[p]
                }
            })
            this.queryParams.set(newValue)
        })
    }

    public update(
        updateFn: (value: Record<Keys[number], string | undefined>) => void,
        skipLocationChange = false
    ) {
        // Get current value from the signal, make a shallow copy
        const oldValue = this.queryParams()
        const newValue = { ...oldValue }

        // Let the caller mutate the new object
        updateFn(newValue)

        // Set a brand-new object to trigger signal reactivity
        this.queryParams.set(newValue)
        this.updateQueryParams(skipLocationChange)
    }

    private updateQueryParams(skipLocationChange: boolean) {
        void this.router.navigate([], {
            relativeTo: this.route,
            queryParams: this.queryParams(),
            queryParamsHandling: 'merge',
            skipLocationChange: skipLocationChange,
        })
    }
}

/**
 * Creates a typed store for the given query parameters, backed by an Angular signal.
 *
 * Usage:
 *   const urlVars = createURLVariables('filter', 'sort');
 *   urlVars.update((v) => { v.filter = 'active'; v.sort = 'desc'; return v});
 * Automatically updates the URL and reactivity whenever values change.
 * Example with Angular effect:
 *   effect(() => {
 *     console.log('Filter changed to:', urlVars.queryParamState().filter);
 *   });
 */
export function createURLVariables<Keys extends string[]>(...paramNames: Keys) {
    return new URLQueryParamStore<[...Keys]>(paramNames)
}
