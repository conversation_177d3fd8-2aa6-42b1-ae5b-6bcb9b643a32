import { AppEnvironment } from '@environments/environment.model'

export const environment: AppEnvironment = {
    production: true,
    apiUrl: 'https://ca-aita-backend.livelybeach-50f34e31.germanywestcentral.azurecontainerapps.io',
    msal: {
        clientId: 'e4d2573d-dac1-44bc-aca1-ce70d90e959b',
        authority:
            'https://login.microsoftonline.com/4b4e036d-f94b-4209-8f07-6860b3641366',
        redirectUri: 'https://red-tree-0d6684903.4.azurestaticapps.net',
        scopes: ['openid', 'profile', 'email'],
        apiScope: ['api://e4d2573d-dac1-44bc-aca1-ce70d90e959b/user.access'],
    },
}
