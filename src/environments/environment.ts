import { AppEnvironment } from '@environments/environment.model'

export const environment: AppEnvironment = {
    production: false,
    apiUrl: 'http://localhost:8080',
    msal: {
        clientId: 'e4d2573d-dac1-44bc-aca1-ce70d90e959b',
        authority:
            'https://login.microsoftonline.com/4b4e036d-f94b-4209-8f07-6860b3641366',
        redirectUri: 'http://localhost:4200',
        scopes: ['openid', 'profile', 'email'], // standard OIDC scopes
        apiScope: ['api://e4d2573d-dac1-44bc-aca1-ce70d90e959b/user.access'],
    },
}
