import { registerLocaleData } from '@angular/common'
import localeDe from '@angular/common/locales/de'
import localeEn from '@angular/common/locales/en'
import localeFi from '@angular/common/locales/fi'
import { enableProdMode } from '@angular/core'
import { bootstrapApplication } from '@angular/platform-browser'

import { AppComponent } from '@app/app.component'
import { appConfig } from '@app/app.config'
import { environment } from '@environments/environment'

if (environment.production) {
    enableProdMode()
}

registerLocaleData(localeDe, 'de-DE')
registerLocaleData(localeFi, 'fi-FI')
registerLocaleData(localeEn, 'en-US')

bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err))
