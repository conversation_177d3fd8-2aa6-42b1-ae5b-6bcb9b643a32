@use '../../node_modules/@angular/material/index' as mat;
@use './theme-colors' as my-theme;

@include mat.core();
$theme: mat.define-theme(
    (
        color: (
            theme-type: light,
            primary: my-theme.$primary-palette,
            tertiary: my-theme.$tertiary-palette,
        ),
    )
);

html {
    @include mat.all-component-themes($theme);
    @include mat.color-variants-backwards-compatibility($theme);
}

@include mat.table-overrides(
    (
        header-headline-font: Cadiz-SemiBold,
        row-item-label-text-font: Cadiz-Regular,
        footer-supporting-text-font: Cadiz-Regular,
        header-headline-color: var(--primary-color),
        row-item-label-text-color: var(--primary-color),
    )
);

@include mat.paginator-overrides(
    (
        container-text-color: var(--primary-color),
        container-text-font: Cadiz-SemiBold,
    )
);

@include mat.datepicker-overrides(
    (
        calendar-date-hover-state-background-color: var(
                --primary-highlighted-color-lighter
            ),
        calendar-container-background-color: var(--white),
        toggle-icon-color: var(--primary-highlighted-color-lighter),
        toggle-active-state-icon-color: var(--primary-highlighted-color-lighter),
    )
);

@include mat.timepicker-overrides(
    (
        container-background-color: var(--white),
    )
);

@include mat.dialog-overrides(
    (
        container-color: var(--white),
        subhead-color: var(--primary-color),
        subhead-font: Messina-Light,
        supporting-text-font: Cadiz-regular,
    )
);

@include mat.tooltip-overrides(
    (
        container-color: var(--primary-color),
        supporting-text-color: var(--white),
        supporting-text-font: Cadiz-regular,
        supporting-text-line-height: 30px,
        container-shape: --mat-sys-corner--small,
    )
);

@include mat.form-field-overrides(
    (
        filled-input-text-placeholder-color: var(--disabled),
        disabled-input-text-placeholder-color: var(--disabled),
        outlined-input-text-placeholder-color: var(--disabled),
    )
);

@include mat.menu-overrides(
    (
        item-label-text-color: var(--primary-color),
        container-color: var(--white),
    )
);

@include mat.expansion-overrides(
    (
        container-background-color: var(--white),
        container-text-color: var(--primary-color),
        header-text-font: Cadiz-Regular,
        container-text-font: Cadiz-Light,
        header-hover-state-layer-color: var(--white),
        container-shape: --mat-sys-corner--small,
    )
);

.mat-expansion-panel-header {
    padding: 0 5px 0 0 !important;
}
.mat-expansion-panel:not([class*='mat-elevation-z']) {
    box-shadow: none !important;
}

@include mat.progress-bar-overrides(
    (
        active-indicator-color: var(--disabled),
        track-color: var(--disabled-darker),
    )
);

@include mat.checkbox-overrides(
    (
        disabled-label-color: var(--disabled),
        disabled-selected-checkmark-color: var(--disabled),
        disabled-selected-icon-color: var(--disabled),
        disabled-unselected-icon-color: var(--disabled),
        label-text-color: var(--primary-color),
        label-text-font: Cadiz-Regular,
        label-text-size: 16px,
        selected-icon-color: var(--white),
        selected-focus-icon-color: var(--white),
        selected-checkmark-color: var(--white),
        selected-pressed-icon-color: var(--white),
        selected-hover-state-layer-color: var(
                --primary-highlighted-color-lighter
            ),
        unselected-hover-state-layer-color: var(
                --primary-highlighted-color-lighter
            ),
        unselected-pressed-state-layer-color: var(
                --primary-highlighted-color-lighter
            ),
        unselected-focus-state-layer-color: var(
                --primary-highlighted-color-lighter
            ),
        selected-pressed-state-layer-color: var(--primary-color),
        selected-focus-state-layer-color: var(--primary-color),
    )
);

.mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background,
.mdc-checkbox__native-control:enabled:indeterminate
    ~ .mdc-checkbox__background {
    border-color: var(
        --primary-color,
        var(--primary-highlighted-color-lighter)
    ) !important;
    background-color: var(
        --primary-color,
        var(--primary-highlighted-color-lighter)
    ) !important;
}

.popup {
    .mdc-button__label {
        color: var(--white);
    }

    @include mat.snack-bar-overrides(
        (
            supporting-text-color: var(--white),
        )
    );

    &.success {
        @include mat.snack-bar-overrides(
            (
                container-color: var(--success),
            )
        );
    }

    &.error {
        @include mat.snack-bar-overrides(
            (
                container-color: var(--error),
            )
        );
    }
}

.input-field-custom-mat-field,
.date-field-custom-mat-field,
.datetime-date-field-custom-mat-field,
.datetime-time-field-custom-mat-field,
.input-textarea-field-custom-mat-field {
    &:hover
        .mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
            .mdc-text-field--focused
        )
        .mdc-notched-outline {
        outline: 1.5px solid var(--primary-highlighted-color-lighter);
    }

    &:focus-within
        .mdc-text-field--outlined:not(
            .mdc-text-field--disabled
        ).mdc-text-field--focused {
        outline: 1px solid var(--primary-highlighted-color);
    }

    .mat-mdc-form-field-icon-prefix {
        padding: 13px 0 13px 20px;
    }

    .mat-mdc-option:focus.mdc-list-item,
    .mat-mdc-option.mat-mdc-option-active.mdc-list-item {
        background-color: var(--primary-highlighted-color-lighter) !important;
    }
}

.datetime-date-field-custom-mat-field,
.datetime-time-field-custom-mat-field {
    .mat-mdc-form-field-infix {
        padding: 0 !important;
    }
}

.datetime-date-field-custom-mat-field {
    margin-bottom: -15px;
}

.mat-timepicker-toggle-default-icon,
.mat-datepicker-toggle-default-icon {
    color: var(--primary-highlighted-color);
}

.metadata-files-container {
    .mdc-list-item__primary-text {
        font-family: Cadiz-BookItalic, serif !important;
        color: var(--disabled);

        &:hover {
            color: var(--disabled);
        }
    }
}

.mat-mdc-tab .mdc-tab__text-label {
    color: var(--primary-color);
    font-family: Messina-Light, Arial, 'Helvetica Neue', sans-serif;
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 300;
    line-height: 3.125rem; /* 125% */
    letter-spacing: -0.05rem;
}
