// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '../../node_modules/@angular/material/index' as mat;

// Note: Color palettes are generated from primary: #0F3D51, secondary: #00819D, tertiary: #FFA572, neutral: #92B1B8
$_palettes: (
    primary: (
        0: #000000,
        10: #001e2c,
        20: #023548,
        25: #134054,
        30: #214b60,
        35: #2e576c,
        40: #3b6379,
        50: #547c92,
        60: #6e96ad,
        70: #88b1c8,
        80: #a3cce5,
        90: #c2e8ff,
        95: #e2f3ff,
        98: #f5faff,
        99: #fbfcff,
        100: #ffffff,
    ),
    secondary: (
        0: #000000,
        10: #001f28,
        20: #003543,
        25: #004151,
        30: #004e60,
        35: #005a6f,
        40: #00677e,
        50: #03829e,
        60: #389cb9,
        70: #58b7d5,
        80: #76d3f1,
        90: #b5ebff,
        95: #dcf5ff,
        98: #f2fbff,
        99: #f9fdff,
        100: #ffffff,
    ),
    tertiary: (
        0: #000000,
        10: #331200,
        20: #542200,
        25: #652a00,
        30: #73350a,
        35: #824016,
        40: #914c21,
        50: #af6436,
        60: #ce7c4d,
        70: #ed9664,
        80: #ffb68e,
        90: #ffdbca,
        95: #ffede5,
        98: #fff8f6,
        99: #fffbff,
        100: #ffffff,
    ),
    neutral: (
        0: #000000,
        10: #001f25,
        20: #16353a,
        25: #224046,
        30: #2e4b51,
        35: #39575d,
        40: #456369,
        50: #5e7c83,
        60: #77969d,
        70: #92b0b7,
        80: #acccd3,
        90: #c8e8ef,
        95: #d6f6fe,
        98: #eefcff,
        99: #f7fdff,
        100: #ffffff,
        4: #001114,
        6: #00161b,
        12: #022429,
        17: #0e2e34,
        22: #1b393f,
        24: #1f3e43,
        87: #c0e0e7,
        92: #ceeef5,
        94: #d3f4fb,
        96: #ddf9ff,
    ),
    neutral-variant: (
        0: #000000,
        10: #161c20,
        20: #2b3135,
        25: #363c41,
        30: #41484c,
        35: #4d5358,
        40: #595f64,
        50: #72787d,
        60: #8b9196,
        70: #a6acb1,
        80: #c1c7cc,
        90: #dde3e8,
        95: #ecf1f7,
        98: #f5faff,
        99: #fbfcff,
        100: #ffffff,
    ),
    error: (
        0: #000000,
        10: #410002,
        20: #690005,
        25: #7e0007,
        30: #93000a,
        35: #a80710,
        40: #ba1a1a,
        50: #de3730,
        60: #ff5449,
        70: #ff897d,
        80: #ffb4ab,
        90: #ffdad6,
        95: #ffedea,
        98: #fff8f7,
        99: #fffbff,
        100: #ffffff,
    ),
);

$_rest: (
    secondary: map.get($_palettes, secondary),
    neutral: map.get($_palettes, neutral),
    neutral-variant: map.get($_palettes, neutral-variant),
    error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);

@function _high-contrast-value($light, $dark, $theme-type) {
    @if ($theme-type == light) {
        @return $light;
    }
    @if ($theme-type == dark) {
        @return $dark;
    }
    @if ($theme-type == color-scheme) {
        @return light-dark(#{$light}, #{$dark});
    }

    @error 'Unknown theme-type #{$theme-type}. Expected light, dark, or color-scheme';
}

@mixin high-contrast-overrides($theme-type) {
    @include mat.theme-overrides(
        (
            primary: _high-contrast-value(#002736, #e1f3ff, $theme-type),
            on-primary: _high-contrast-value(#ffffff, #000000, $theme-type),
            primary-container: _high-contrast-value(
                    #0f3d51,
                    #9fc8e1,
                    $theme-type
                ),
            on-primary-container: _high-contrast-value(
                    #f6faff,
                    #000d15,
                    $theme-type
                ),
            inverse-primary: _high-contrast-value(#a3cce5, #234c61, $theme-type),
            primary-fixed: _high-contrast-value(#244e62, #c2e8ff, $theme-type),
            primary-fixed-dim: _high-contrast-value(
                    #05374b,
                    #a3cce5,
                    $theme-type
                ),
            on-primary-fixed: _high-contrast-value(
                    #ffffff,
                    #000000,
                    $theme-type
                ),
            on-primary-fixed-variant: _high-contrast-value(
                    #ffffff,
                    #00131d,
                    $theme-type
                ),
            secondary: _high-contrast-value(#00313d, #daf4ff, $theme-type),
            on-secondary: _high-contrast-value(#ffffff, #000000, $theme-type),
            secondary-container: _high-contrast-value(
                    #005063,
                    #72cfed,
                    $theme-type
                ),
            on-secondary-container: _high-contrast-value(
                    #ffffff,
                    #000d13,
                    $theme-type
                ),
            secondary-fixed: _high-contrast-value(#005063, #b5ebff, $theme-type),
            secondary-fixed-dim: _high-contrast-value(
                    #003846,
                    #76d3f1,
                    $theme-type
                ),
            on-secondary-fixed: _high-contrast-value(
                    #ffffff,
                    #000000,
                    $theme-type
                ),
            on-secondary-fixed-variant: _high-contrast-value(
                    #ffffff,
                    #00141a,
                    $theme-type
                ),
            tertiary: _high-contrast-value(#3f1800, #ffece4, $theme-type),
            on-tertiary: _high-contrast-value(#ffffff, #000000, $theme-type),
            tertiary-container: _high-contrast-value(
                    #612800,
                    #ffb185,
                    $theme-type
                ),
            on-tertiary-container: _high-contrast-value(
                    #fff9f7,
                    #190600,
                    $theme-type
                ),
            tertiary-fixed: _high-contrast-value(#76370d, #ffdbca, $theme-type),
            tertiary-fixed-dim: _high-contrast-value(
                    #572300,
                    #ffb68e,
                    $theme-type
                ),
            on-tertiary-fixed: _high-contrast-value(
                    #ffffff,
                    #000000,
                    $theme-type
                ),
            on-tertiary-fixed-variant: _high-contrast-value(
                    #ffffff,
                    #230a00,
                    $theme-type
                ),
            background: _high-contrast-value(#eefcff, #00161b, $theme-type),
            on-background: _high-contrast-value(#001f25, #c8e8ef, $theme-type),
            surface: _high-contrast-value(#eefcff, #00161b, $theme-type),
            surface-dim: _high-contrast-value(#9fbec5, #00161b, $theme-type),
            surface-bright: _high-contrast-value(#eefcff, #37555b, $theme-type),
            surface-container-lowest: _high-contrast-value(
                    #ffffff,
                    #000000,
                    $theme-type
                ),
            surface-container: _high-contrast-value(
                    #c8e8ef,
                    #16353a,
                    $theme-type
                ),
            surface-container-high: _high-contrast-value(
                    #badae1,
                    #224046,
                    $theme-type
                ),
            surface-container-highest: _high-contrast-value(
                    #acccd3,
                    #2e4b51,
                    $theme-type
                ),
            on-surface: _high-contrast-value(#000000, #ffffff, $theme-type),
            shadow: _high-contrast-value(#000000, #000000, $theme-type),
            scrim: _high-contrast-value(#000000, #000000, $theme-type),
            surface-tint: _high-contrast-value(#3b6379, #a3cce5, $theme-type),
            inverse-surface: _high-contrast-value(#16353a, #c8e8ef, $theme-type),
            inverse-on-surface: _high-contrast-value(
                    #ffffff,
                    #000000,
                    $theme-type
                ),
            outline: _high-contrast-value(#272d31, #ebf1f6, $theme-type),
            outline-variant: _high-contrast-value(#444a4e, #bdc3c9, $theme-type),
            error: _high-contrast-value(#600004, #ffece9, $theme-type),
            on-error: _high-contrast-value(#ffffff, #000000, $theme-type),
            error-container: _high-contrast-value(#98000a, #ffaea4, $theme-type),
            on-error-container: _high-contrast-value(
                    #ffffff,
                    #220001,
                    $theme-type
                ),
            surface-variant: _high-contrast-value(#dde3e8, #41484c, $theme-type),
            on-surface-variant: _high-contrast-value(
                    #000000,
                    #ffffff,
                    $theme-type
                ),
        )
    );
}
