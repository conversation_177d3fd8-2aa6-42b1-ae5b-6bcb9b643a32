// colors
$primary-color: #0f3d51;
$primary-highlighted-color: #00819d;
$primary-highlighted-color-lighter: #b8eaf1;
$surface-light: #f7f8f8;
$surface-third: #d2e2e5;
$white: #ffffff;
$black: #000000;
$disabled: #92b1b8;
$disabled-lighter: #d3e3e5;
$disabled-darker: #627a80;
$secondary-highlighted-color: #f5faff;
$info: #00819d;
$error: #e32b2b;
$success: #00892f;
$gofore-orange: #f7673b;

// spacing / sizing
$margin-section: 1rem;
$margin-vertical-gap: 0.75rem;
$margin-horizontal-pad: 0.5rem;
$margin-horizontal-pad-large: 1.5rem;
$margin-vertical-pad: 0.5rem;
$margin-vertical-pad-large: 1.5rem;
$margin-text-icon: 0.25rem;

$border-radius: 0.25rem;

$header-height: 95px;
$footer-height: 95px;

// custom properties
:root {
    --primary-color: #{$primary-color};
    --primary-highlighted-color: #{$primary-highlighted-color};
    --primary-highlighted-color-lighter: #{$primary-highlighted-color-lighter};
    --secondary-highlighted-color: #{$secondary-highlighted-color};
    --surface-light: #{$surface-light};
    --surface-third: #{$surface-third};
    --white: #{$white};
    --black: #{$black};
    --disabled: #{$disabled};
    --disabled-lighter: #{$disabled-lighter};
    --disabled-darker: #{$disabled-darker};
    --info: #{$info};
    --error: #{$error};
    --success: #{$success};
    --gofore-orange: #{$gofore-orange};

    --margin-section: #{$margin-section};
    --margin-vertical-gap: #{$margin-vertical-gap};
    --margin-horizontal-pad: #{$margin-horizontal-pad};
    --margin-vertical-pad: #{$margin-vertical-pad};
    --margin-horizontal-pad-large: #{$margin-horizontal-pad-large};
    --margin-vertical-pad-large: #{$margin-vertical-pad-large};
    --margin-text-icon: #{$margin-text-icon};

    --border-radius: #{$border-radius};

    --header-height: $header-height;
    --footer-height: $footer-height;

    // TODO: get this in order and use it
    --layout-width-mobile: 100%;
    --layout-width-tablet: 720px;
    --layout-width-laptop: 960px;
    --layout-width-large-laptop: 1200px;
    --layout-width-desktop: 1360px;
    --layout-width-large-desktop: 1520px;
    --layout-width-ultrawide: 1800px;
    --layout-width-4k: 2400px;

    --layout-padding-mobile: 1rem;
    --layout-padding-tablet: 2rem;
    --layout-padding-laptop: 2rem;
    --layout-padding-large-laptop: 3rem;
    --layout-padding-desktop: 3rem;
    --layout-padding-large-desktop: 4rem;
    --layout-padding-ultrawide: 4rem;
    --layout-padding-4k: 6rem;
}
