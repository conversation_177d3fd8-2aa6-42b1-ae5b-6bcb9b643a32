@use './variables';
@use './fonts';
@use './mat';

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
    height: 100%;
    box-sizing: border-box;
    color: var(--primary-color);
    margin: 0;
    padding: 0;
}

body {
    margin: 0;
    font-family: Cadiz-Regular, Arial, 'Helvetica Neue', sans-serif;
}

.mat-icon svg {
    height: 24px;
    width: 24px;
}

.primary-color {
    color: var(--primary-color);
}

.primary-color-lighter {
    color: var(--primary-highlighted-color-lighter);
}

.primary-color-highlighted {
    color: var(--primary-highlighted-color);
}

.success-color {
    color: var(--success);
}

.disabled-color {
    color: var(--disabled);
}

/* ===== Global Markdown Content Styles ===== */

// for analysis markdown styles
@layer components {
    .analysis-value markdown {
        @apply block leading-relaxed;

        h1 {
            @apply text-3xl font-semibold mb-4 border-b border-gray-200 pb-2;
        }
        h2 {
            @apply text-2xl font-semibold my-3;
        }

        p {
            @apply my-2;
        }

        a {
            @apply text-blue-600 underline hover:no-underline;
        }

        code {
            @apply bg-gray-100 px-1 py-0.5 rounded font-mono text-sm;
        }

        pre {
            @apply bg-gray-800 text-gray-100 p-4 rounded overflow-x-auto font-mono text-sm;
        }

        blockquote {
            @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
        }

        ul,
        ol {
            @apply ml-6 my-2;
        }

        table {
            @apply w-full border-collapse my-4;
            th,
            td {
                @apply border border-gray-300 p-2 text-left;
            }
            th {
                @apply bg-gray-100;
            }
        }
    }
}

.analysis-value markdown {
    strong {
        font-weight: 800;
        font-family:
            Cadiz-Bold Arial,
            'Helvetica Neue',
            sans-serif;
    }
}

// markdown editor component styles
.md-editor .preview h1,
.md-editor .preview h2,
.md-editor .preview h3,
.md-editor .preview h4,
.md-editor .preview h5,
.md-editor .preview h6 {
    margin: 1.5em 0 0.5em;
    font-weight: 600;
    line-height: 1.25;
}

.md-editor .preview h1 {
    font-size: 2em;
}
.md-editor .preview h2 {
    font-size: 1.5em;
}
.md-editor .preview h3 {
    font-size: 1.25em;
}
.md-editor .preview h4 {
    font-size: 1.1em;
}
.md-editor .preview h5 {
    font-size: 1em;
}
.md-editor .preview h6 {
    font-size: 0.9em;
}

.md-editor .preview p {
    margin: 1em 0;
    line-height: 1.6;
}

.md-editor .preview strong {
    font-weight: 800;
    font-family:
        Cadiz-Bold Arial,
        'Helvetica Neue',
        sans-serif;
}

.md-editor .preview em {
    font-style: italic;
}

.md-editor .preview code {
    background: var(--muted, #f8fafc);
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    font-size: 0.9em;
}

.md-editor .preview pre {
    background: var(--muted, #f8fafc);
    padding: 1em;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1em 0;
}
.md-editor .preview pre code {
    background: none;
    padding: 0;
}

.md-editor .preview blockquote {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid var(--border, #e2e8f0);
    background: var(--muted, #f8fafc);
    color: var(--muted-foreground, #64748b);
}
.md-editor .preview blockquote p {
    margin: 0.5em 0;
}

.md-editor .preview ul,
.md-editor .preview ol {
    margin: 1em 0;
    padding-left: 2em;
}
.md-editor .preview li {
    margin: 0.5em 0;
}

.md-editor .preview table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}
.md-editor .preview th,
.md-editor .preview td {
    padding: 0.5em 1em;
    text-align: left;
    border-bottom: 1px solid var(--border, #e2e8f0);
}
.md-editor .preview th {
    font-weight: 600;
    background: var(--muted, #f8fafc);
}

.md-editor .preview hr {
    border: none;
    height: 1px;
    background: var(--border, #e2e8f0);
    margin: 2em 0;
}

.md-editor .preview a {
    color: var(--primary, #3b82f6);
    text-decoration: none;
}
.md-editor .preview a:hover {
    text-decoration: underline;
}

.md-editor .preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1em 0;
}

.md-editor .preview {
    ul {
        list-style-type: disc;
    }
    ol {
        list-style-type: decimal;
    }
    ul ul,
    ol ul {
        list-style-type: circle;
        list-style-position: inside;
        margin-left: 15px;
    }
    ol ol,
    ul ol {
        list-style-type: lower-latin;
        list-style-position: inside;
        margin-left: 15px;
    }
}

.md-editor .preview ul li.task-list-item {
    list-style: none;
    margin-left: -1.5em;
}
.md-editor .preview ul li.task-list-item input[type='checkbox'] {
    margin-right: 0.5em;
}
