/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ['./src/**/*.{html,ts,scss}'],
    theme: {
        extend: {
            colors: {
                primary: 'var(--primary-color)',
                primaryHighlightedColor: 'var(--primary-highlighted-color)',
                primaryHighlightedColorLighter:
                    'var(--primary-highlighted-color-lighter)',
                surfaceLight: 'var(--surface-light)',
                surfaceThird: 'var(--surface-third)',
                white: 'var(--white)',
                black: 'var(--black)',
            },
            spacing: {
                section: 'var(--margin-section)',
                verticalGap: 'var(--margin-vertical-gap)',
                horizontalPad: 'var(--margin-horizontal-pad)',
                verticalPad: 'var(--margin-vertical-pad)',
                textIcon: 'var(--margin-text-icon)',
            },
            borderRadius: {
                standard: 'var(--border-radius)',
            },
        },
    },
    plugins: [],
}
